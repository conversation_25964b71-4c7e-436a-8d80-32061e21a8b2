<?php

class ReceivingRecords {


    public static function updateMany($con, $waybill_array, $update_pairs ){
        $waybill_ids = array_column($waybill_array, 'receiving_waybill_id');
        $update_string = '';

        try{
        // build up the update string
        foreach ($update_pairs as $key => $value) {
            $update_string .= "{$key}='{$value}',";
            }
        $chunks = array_chunk($waybill_ids, 65000);
        foreach($chunks as $eachChunk){
        $placeholder_string = join(",", array_fill(0,count($eachChunk), "?"));
        $query = "UPDATE harvest_receiving_record SET ${update_string} updated_at = NOW() WHERE receiving_waybill_id IN ({$placeholder_string})";
        $stmt = $con->prepare($query);
        $stmt->execute(array_values($eachChunk));
        }
       }
    catch(Exception $err){
        throw new Exception("Ooops!!! Looks like we couldn't update all checker flags - ". $err->getMessage());
                }
    }
}


?>