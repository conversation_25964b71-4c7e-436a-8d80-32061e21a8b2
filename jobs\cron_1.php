<?php

require_once(__DIR__ . "/../app/db/cleared/ClearedDbOperations.php");
require_once(__DIR__ . "/../app/db/config/ConfigDbOperations.php");
require_once(__DIR__ . "/../app/models/ClearedData.php");
require_once(__DIR__ . "/../app/controllers/Cast.php");
require_once(__DIR__ . "/../app/controllers/Logger.php");
require_once(__DIR__ . "/../app/controllers/__calculations.php");
require_once(__DIR__ . "/../app/db/receiving/ReceivingDbOperations.php");

/**
 * Cron 1 serves multiple functions, one of which is to compute the prorated total weight, net weight marketed, weight of empty bag, transportation, threshing, processing cost
 * as well as total cost. It also checks the quality metrics of records on the harvest cleared records, and in the event that it spots a record that was not flagged for the verifier, 
 * the record gets taken out of the harvest cleared record and sent for the verifier review. One final action this cron carries out is to set the transferred_flag and updadted_flag to 
 * 0 to make way for Cron 2 to pick the just updated records and transfer to the harvest member payment table.
 * 
 */

class ValidateClearedTableJob
{
    private ClearedDbOperations $clearedDbOperations;
    private ReceivingDbOperations $receivingDbOperations;

    private array $logs;

    function __construct($pg_con)
    {
        $this->clearedDbOperations = new ClearedDbOperations($pg_con);
        $this->receivingDbOperations = new ReceivingDbOperations($pg_con);

        $this->logs = array();
    }

    private function trim_str($str)
    {
        return trim($str);
    }

    function validate_cleared_data($config, $conn_transport, $conn_inventory, $conn_farming): array
    {
        echo  date('Y-m-d H:i:s.u') . "=====Cron 1 Started==== <br /> \n";
        $startTime = microtime(true);
        try {
            $this->logs = array();
            $this->logs[] = $this->getLogString("Fetching Data from harvest cleared records table");
            //Fetches all the record from the harvest cleared table where update flag is 0
            //change hsf to waybill id
            $records = $this->clearedDbOperations->get_data();
            $this->logs[] = $this->getLogString(count($records) . " data fetched successfully");

            // $transportData = $this->calculateTransportCost($conn_transport);
            // $transport_cost_map = [];

            // foreach ($transportData as $cost_data) {
            //     $transport_cost_map[$cost_data['unique_member_id']] = $cost_data['total_cost'];
            // }
            //$harvestReceivingData = $this->fetchHarvestReceivingRecord($conn_inventory);
            // $manualThreshingData = $this->fetchManualConfirmThreshingData($conn_farming);
            // $mergedRecords = $this->MergeManualAndConfirmThreshingRecord($confirmThreshingData, $manualThreshingData);


            //Fetches transportation_flag, threshing_flag, total_field_size_threshed,transaction_date, bags_received from harvest_receiving_record
            //Doubt if this is still necessary
            //Change hsf to receiving waybill id
            $receiving_records = $this->receivingDbOperations->get_data();
            //print_r($receiving_records);
            $this->logs[] = $this->getLogString("Fetching Data from receiving records table");
            //$this->logs[] = $this->getLogString(count($receiving_records) . " records fetched successfully");

            $flaggedRecords = array();
            $unFlaggedRecords = array();

            $quality_check_exclude_list = array_map(array($this, 'trim_str'), explode(",", $config->quality_check_exclude_list));

            foreach ($records as $record) {
                try {
                    $data = castClearedData($record);
                    $this->logs[] = $this->getLogString("Checking Waybill Id: {$data->getWaybillId()}");
                    //print_r($data);

                    if (!in_array($data->getVerifierId(), $quality_check_exclude_list)) {
                        $hasFlags = $this->check_quality_metric($data, $config);
                    }
                    $this->compute_data($data);
                    $this->compute_cost($data, $receiving_records, $config);
                    $hasFlags = $this->check_average_weight($data, $config, $quality_check_exclude_list) || $hasFlags;

                    if ($hasFlags) {
                        $this->logs[] = $this->getLogString("Waybill Id: {$data->getWaybillId()} has flags");
                        $flaggedRecords[] = $data;
                    } else {
                        $unFlaggedRecords[] = $data;
                        $this->logs[] = $this->getLogString("Waybill Id: {$data->getWaybillId()} has no flags");
                    }
                    $this->logs[] = "<br>";
                } catch (TypeError $error) {
                    $this->logs[] = $this->getLogString("Waybill Id: {$data->getWaybillId()} has an error: Error: {$error->getMessage()}");
                    $flaggedRecords[] = $data;
                    $this->logs[] = "<br>";
                } catch (Exception $error) {
                    $this->logs[] = $this->getLogString("Waybill Id: {$data->getWaybillId()} has an error: Error: {$error->getMessage()}");
                    $flaggedRecords[] = $data;
                    $this->logs[] = "<br>";
                }
            }

            $this->logs[] = $this->getLogString("Flagged Records: " . count($flaggedRecords));
            $this->logs[] = $this->getLogString("UnFlagged Records: " . count($unFlaggedRecords));

            /**
             * The block of code below handles updating verifier flag on the harvest checking records for the verifier to review
             */
            $this->logs[] = $this->getLogString("Flagging records to verifier");
            $this->clearedDbOperations->flag_records_to_verifier($flaggedRecords, 2000);


            /**
             * COMMENT ID: CRON_1_B
             * The block of code below handles taking out any records from the harvest cleared records that has failed disparity checks or quality checks, this solves 
             * the issue where a manual upload is done into the harvest cleared records table.
             * 
             * This block of code also handles the scenario where the verifier who audits a transaction makes a mistake on the record they verified. when these errors are made 
             * and the cron service audits the records, this system has the ability to return the transaction to the table of the verifier, however there is an exception where the system does not 
             * return this records, this is possible only if the verifier's id is specified in the override config
             */

            $this->logs[] = $this->getLogString("Deleting Flagged Records from cleared table");
            $this->clearedDbOperations->delete_records($flaggedRecords, 2000);

            if (count($unFlaggedRecords) > 0) {
                $this->logs[] = $this->getLogString("Updating data for unFlagged Records");
                $this->clearedDbOperations->update_records($unFlaggedRecords);
            }

            // UPDATE CASH LOCAL 
            $this->clearedDbOperations->updateCashLocal();

            $this->logs[] = $this->getLogString("End of job");
        } catch (Exception $exception) {
            $this->logs[] = $this->getLogString("Error occurred: {$exception->getMessage()}");
        }
        $endTime = microtime(true);

        $timeElapsed = ($endTime - $startTime);
        $this->logs[] = $this->getLogString("Job ran in {$timeElapsed}s");
        return $this->logs;
    }

    private function getLogString($message): string
    {
        return getLogString("CRON 1", $message);
    }

    /**
     * Comment ID: CRON_1_C
     * This function completes analysis on the quality metrics again, it does this based on the comment CRON_1_B
     * 
     */
    private function check_quality_metric(ClearedData $data,  $config): bool
    {
        $this->logs[] = $this->getLogString("Checking Quality Metric for Waybill: {$data->getWaybillId()}");

        $hasFlags = false;

        if ($data->getMoldyGrainscount() > $config->moldiness_config) {
            $hasFlags = true;
            $this->logs[] = $this->getLogString("Moldy grain count flagged for Waybill: {$data->getWaybillId()}");
            $data->setMoldyGrainscountflag(1);
        }

        if ($data->getCleanlinessPercentage() < $config->cleanliness_config) {
            $this->logs[] = $this->getLogString("Cleanliness percentage flagged for Waybill: {$data->getWaybillId()}; Cleanliness percentage: {$data->getCleanlinessPercentage()}; Cleanliness config: $config->cleanliness_config;");
            $hasFlags = true;
        }

        if ($data->getMoisturePercentage() > $config->moisture_config) {
            $this->logs[] = $this->getLogString("Moisture percentage flagged for Waybill: {$data->getWaybillId()}; Moisture percentage: {$data->getMoisturePercentage()}; Moisture config: {$config->moisture_config};");
            $hasFlags = true;
        }

        if (!$hasFlags) {
            $this->logs[] = $this->getLogString("No Flags for Waybill Id: {$data->getWaybillId()}");
        }

        return $hasFlags;
    }

    private function check_average_weight(ClearedData $data, $config, $quality_check_exclude_list): bool
    {
        $this->logs[] = $this->getLogString("Checking Net Weight and Bags Marketed relationship for Waybill: {$data->getWaybillId()}");

        $hasFlags = false;

        try {
            if ($data->getBagsMarketed() == 0) {
                throw new Exception("Division by zero error");
            }
            $average_weight = $data->getNetWeight() / $data->getBagsMarketed();
            $data->setAverageWeight($this->round_num($average_weight));

            if (in_array($data->getVerifierId(), $quality_check_exclude_list)) {
                return false;
            }
            $min_config = 0;
            $max_config = 0;
            switch (strtolower($data->getProductType())) {
                case "maize":
                    $min_config = $config->average_weight_min_maize_config;
                    $max_config = $config->average_weight_max_maize_config;
                    break;
                case "soy":
                    $min_config = $config->average_weight_min_soy_config;
                    $max_config = $config->average_weight_max_soy_config;
                    break;
                case "rice":
                    $min_config = $config->average_weight_min_rice_config;
                    $max_config = $config->average_weight_max_rice_config;
                    break;
                default:
                    break;
            }

            if (strpos(strtolower($data->getProductType()), "soy") !== false) {
                $min_config = $config->average_weight_min_soy_config;
                $max_config = $config->average_weight_max_soy_config;
            }

            if (
                intval($average_weight) < $min_config ||
                intval($average_weight) > $max_config
            ) {
                $this->logs[] = $this->getLogString("Net Weight and Bags Marketed relationship flagged for : {$data->getWaybillId()}");
                $hasFlags = true;
            }
            $this->logs[] = $this->getLogString("Net Weight for : {$data->getWaybillId()} - {$data->getNetWeight()}");
            $this->logs[] = $this->getLogString("Bags Marketed for : {$data->getWaybillId()} - {$data->getBagsMarketed()}");
            $this->logs[] = $this->getLogString("Average Weight for : {$data->getWaybillId()} - {$average_weight}");
            $this->logs[] = $this->getLogString("Average Weight Min Config - {$min_config}");
            $this->logs[] = $this->getLogString("Average Weight Max Config - {$max_config}");
            $this->logs[] = $this->getLogString("Product type - {$data->getProductType()}");


            if (!$hasFlags) {
                $this->logs[] = $this->getLogString("No Net Weight/Bags Marketed Flags for Waybill: {$data->getWaybillId()}");
            }
            return $hasFlags;
        } catch (Exception $exception) {
            $this->logs[] = $this->getLogString("An exception occurred: {$exception->getMessage()}");
            return true;
        }
    }

    private function round_num($num): float
    {
        return round($num, 2);
    }

    /**
     * The block of code below computes the prorated total weight, weight of empty bag, net weight marketed on the harvest cleared records.
     * 
     */
    private function compute_data(ClearedData $data)
    {
        $this->logs[] = $this->getLogString("Computing weight data for Waybill Id: {$data->getWaybillId()}");

        $prorated_weight = calculate_prorated_total_weight($data->getTotalWeight(), $data->getMoisturePercentage());
        $empty_bag = calculate_weight_of_empty_bag($data->getBagsMarketed());
        $net_weight_marketed = calculate_net_weight_marketed($empty_bag, $prorated_weight);
        $data->setProratedTotalweight($this->round_num($prorated_weight));
        $data->setEmptyBagweight($this->round_num($empty_bag));
        $data->setNetWeight($this->round_num($net_weight_marketed));

        $this->logs[] = $this->getLogString("Prorated Total Weight for Waybill: {$data->getWaybillId()}; Prorated Total weight: {$data->getProratedTotalweight()}");
        $this->logs[] = $this->getLogString("Empty Bag Weight for Waybill: {$data->getWaybillId()}; Empty Bag weight: {$data->getEmptyBagweight()}");
        $this->logs[] = $this->getLogString("Net Weight for Waybill: {$data->getWaybillId()}; Net weight: {$data->getNetWeight()}");
    }

    // private function compute_cost(ClearedData $data, $receiving_records, $config, $transport_cost_data)
    // {
    //     $transport_cost = $data->getTransporterCost();
    //     $threshing_cost = $data->getThreshingCost();

    //     $confirmThreshingCost = 0;
    //     $harvestReceivingCost = 0;

    //     $this->logs[] = $this->getLogString("Initial transporter cost: " . $transport_cost);
    //     $this->logs[] = $this->getLogString("Initial threshing cost: " . $threshing_cost);


    //     if (!is_numeric($transport_cost)) {
    //         $transport_cost = 0;
    //     }

    //     if (!is_numeric($threshing_cost)) {
    //         $threshing_cost = 0;
    //     }

    //     $member_id = $data->getUniqueMemberid();
    //     $this->logs[] = $this->getLogString("Member id: " . $member_id);

    //         //$transport_cost_data = $this->calculateTransportCost($conn);

    //         // Filter the transport cost data to find the matching unique_member_id
    //         // Fetch the transport costs for the given member_id
        
    //         // Check if the result is an array before using foreach
    //     // if (is_array($transport_cost_data) && !empty($transport_cost_data)) {
    //     //     // Filter the transport cost data to find the matching unique_member_id
    //     //     foreach ($transport_cost_data as $cost_data) {
    //     //         if ($cost_data['unique_member_id'] == $member_id) {
    //     //             $transport_cost = $cost_data['total_cost'];
    //     //             $this->logs[] = $this->getLogString("Transport cost now: " . $transport_cost);
    //     //             break;
    //     //         }
    //     //     }
    //     // } else {
    //     //     $this->logs[] = $this->getLogString("No transport cost data found for member_id $member_id.");
    //     // }

    //     // $this->logs[] = $this->getLogString("Calculated Transport cost for member_id $member_id: " . $transport_cost);
    //     // $this->logs[] = $this->getLogString("Transport cost: " . $transport_cost);


    //     if (isset($receiving_records[$data->getWaybillId()])) {
    //         $receiving_data = $receiving_records[$data->getWaybillId()];
    //         $this->logs[] = $this->getLogString("Transport flag: " . $receiving_data->getTransportationFlag());

    //         // $transport_info_id = $receiving_data->getTransportInfoId();
    //         // $this->logs[] = $this->getLogString("Fetched Transport info id: " . $transport_info_id);

    //         // $transport_cost = $this->getTotalCostByTransportInfoId($transport_info_id, $conn);
    //         // $this->logs[] = $this->getLogString("Transport cost: " . $transport_cost);
            
    //         $member_id = $receiving_data->getUniqueMemberid();
    //         $this->logs[] = $this->getLogString("Fetched Transport info id: " . $member_id);

    //         //$transport_cost_data = $this->calculateTransportCost($conn);

    //         // Filter the transport cost data to find the matching unique_member_id
    //         // Fetch the transport costs for the given member_id
        
    //         // Check if the result is an array before using foreach
    //         if (is_array($transport_cost_data) && !empty($transport_cost_data)) {
    //             // Filter the transport cost data to find the matching unique_member_id
    //             foreach ($transport_cost_data as $cost_data) {
    //                 if ($cost_data['unique_member_id'] == $member_id) {
    //                     $transport_cost = $cost_data['total_cost'];
    //                     break;
    //                 }
    //             }
    //         } else {
    //             $this->logs[] = $this->getLogString("No transport cost data found for member_id $member_id.");
    //         }

    //         $this->logs[] = $this->getLogString("Calculated Transport cost for member_id $member_id: " . $transport_cost);
    //         $this->logs[] = $this->getLogString("Transport cost: " . $transport_cost);

    //         $this->logs[] = $this->getLogString("Threshing flag: " . $receiving_data->getThreshingFlag());
    //     }

    //     // $uniqueMemberId = $data->getUniqueMemberId();
    //     // $this->logs[] = $this->getLogString("Unique Member ID: " . $uniqueMemberId);


    //     // foreach ($confirmThreshingData as $ctData) {
    //     //     if ($ctData['unique_member_id'] == $uniqueMemberId) {
    //     //         $confirmThreshingCost = $ctData['cost'];
    //     //         $this->logs[] = $this->getLogString("Merged Threshing Cost: " . $confirmThreshingCost);
    //     //         break;
    //     //     }
    //     // }

    //     // foreach ($harvestReceivingData as $hrData) {
    //     //     if ($hrData['unique_member_id'] == $uniqueMemberId) {
    //     //         $harvestReceivingCost = $hrData['cost'];
    //     //         $this->logs[] = $this->getLogString("Harvest Receiving Cost: " . $harvestReceivingCost);
    //     //         break;
    //     //     }
    //     // }

    //     // if ($confirmThreshingCost === $harvestReceivingCost) {
    //     //     $threshing_cost = $confirmThreshingCost;
    //     //     $this->logs[] = $this->getLogString("Threshing cost is equal for both, selected cost: " . $threshing_cost);
    //     // } elseif ($harvestReceivingCost >= $confirmThreshingCost) {
    //     //     $threshing_cost = $harvestReceivingCost;
    //     //     $this->logs[] = $this->getLogString("Harvest Receiving Cost is greater, selected cost: " . $threshing_cost);
    //     // } else {
    //     //     $threshing_cost = $confirmThreshingCost;
    //     //     $this->logs[] = $this->getLogString("Merged Threshing Cost is selected: " . $threshing_cost);
    //     // }

    //     $cost_per_bag = 0;
    //     switch ($data->getProductType()) {
    //         case "maize":
    //             $cost_per_bag = $config->processing_maize_config;
    //             break;
    //         case "soy":
    //             $cost_per_bag = $config->processing_soy_config;
    //             break;
    //         case "rice":
    //             $cost_per_bag = $config->processing_rice_config;
    //             break;
    //         default:
    //             break;
    //     }

    //     $processing_cost = calculate_cost($cost_per_bag, $data->getBagsMarketed());
    //     $this->logs[] = $this->getLogString("Processing Cost per bag: " . $cost_per_bag . " for " . $data->getProductType() . "; Total bag marketed: " . $data->getBagsMarketed());

    //     $cost = $transport_cost + $threshing_cost + $processing_cost;

    //     $this->logs[] = $this->getLogString("Transportation Cost: " . $transport_cost);
    //     $this->logs[] = $this->getLogString("Threshing Cost: " . $threshing_cost);
    //     $this->logs[] = $this->getLogString("Processing Cost: " . $processing_cost);
    //     $this->logs[] = $this->getLogString("Total Cost: " . $cost);

    //     $data->setThreshingCost($threshing_cost);
    //     $data->setTransporterCost($transport_cost);
    //     $data->setCcProcessingcost($processing_cost);
    //     $data->setCosts($cost);
    // }

    private function compute_cost(ClearedData $data, $receiving_records, $config)
    {
        $transport_cost = $data->getTransporterCost();
        $threshing_cost = $data->getThreshingCost();


        $this->logs[] = $this->getLogString("Initial transporter cost: " . $transport_cost);
        $this->logs[] = $this->getLogString("Initial threshing cost: " . $threshing_cost);


        if (!is_numeric($transport_cost)) {
            $transport_cost = 0;
        }

        if (!is_numeric($threshing_cost)) {
            $threshing_cost = 0;
        }

        // $member_id = $data->getUniqueMemberid();
        // $this->logs[] = $this->getLogString("Member id: " . $member_id);

        // if (isset($transport_cost_data[$member_id])) {
        //     $transport_cost = $transport_cost_data[$member_id];
        // } else {
        //     $this->logs[] = $this->getLogString("No transport cost data found for member_id $member_id.");
        // }
        // $this->logs[] = $this->getLogString("Calculated Transport cost for member_id $member_id: " . $transport_cost);

        
        $cost_per_bag = 0;
        switch ($data->getProductType()) {
            case "maize":
                $cost_per_bag = $config->processing_maize_config;
                break;
            case "soy":
                $cost_per_bag = $config->processing_soy_config;
                break;
            case "rice":
                $cost_per_bag = $config->processing_rice_config;
                break;
            default:
                break;
        }

        $processing_cost = calculate_cost($cost_per_bag, $data->getBagsMarketed());
        $this->logs[] = $this->getLogString("Processing Cost per bag: " . $cost_per_bag . " for " . $data->getProductType() . "; Total bag marketed: " . $data->getBagsMarketed());

        $cost = $transport_cost + $threshing_cost + $processing_cost;

        $this->logs[] = $this->getLogString("Transportation Cost: " . $transport_cost);
        $this->logs[] = $this->getLogString("Threshing Cost: " . $threshing_cost);
        $this->logs[] = $this->getLogString("Processing Cost: " . $processing_cost);
        $this->logs[] = $this->getLogString("Total Cost: " . $cost);

        $data->setThreshingCost($threshing_cost);
        $data->setTransporterCost($transport_cost);
        $data->setCcProcessingcost($processing_cost);
        $data->setCosts($cost);
    }


    function fetchDeliveryCodeToPriceMapping($conn)
    {
        $query = "SELECT code, price FROM price_code_mapping WHERE deactivate_flag = 0";

        $stmt = $conn->prepare($query);
        $stmt->execute();

        $results = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

        return $results;
    }

    function fetchIdToFieldSizeMapping($conn)
    {
        $query = "SELECT unique_field_id, field_size FROM mapped_field";

        $stmt = $conn->prepare($query);
        $stmt->execute();

        $results = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

        return $results;
    }

    private function getDeliveryCodeByTransportInfoId($transport_info_id, $conn)
    {
        $stmt = $conn->prepare("SELECT delivery_code FROM transport_info WHERE transport_info_id = :transport_info_id LIMIT 1");

        $stmt->bindParam(':transport_info_id', $transport_info_id, PDO::PARAM_INT);

        $stmt->execute();

        $result = $stmt->fetch(PDO::FETCH_OBJ);

        if ($result && strlen($result->delivery_code) >= 3) {
            return substr($result->delivery_code, -3);
        }

        return null;
    }

    private function getTotalCostByTransportInfoId($transport_info_id, $conn)
    {
        try {
            $query = "
                SELECT total_cost
                FROM transport_controller
                WHERE transport_info_id = :transport_info_id
            ";

            $stmt = $conn->prepare($query);
            $stmt->bindParam(':transport_info_id', $transport_info_id, PDO::PARAM_INT);
            $stmt->execute();

            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            return $result ? $result['total_cost'] : 0;
        } catch (Exception $err) {
            $this->logs[] = $this->getLogString("Error fetching total cost: " . $err->getMessage());
            return 0;
        }
    }


    function fetchConfirmThreshingData($conn)
    {
        $query = "select unique_member_id, sum(field_size), sum(cost) from (

            SELECT unique_member_id, field_size, field_size * 5990 AS cost, ik_number
                              FROM public.confirm_threshing
                              WHERE delete_flag = 0 AND type_of_thresher = 'COMPANY' AND unique_field_id NOT IN (SELECT unique_field_id FROM manual_confirm_threshing WHERE LOWER(type_of_thresher) = 'company')
                              
                union all
                              
            SELECT unique_member_id, field_size, field_size * 5990 AS cost, ik_number
                    FROM public.manual_confirm_threshing
                    WHERE delete_flag = 0 AND LOWER(type_of_thresher) = 'company') as u
                    group by u.unique_member_id";

        $stmt = $conn->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    function fetchHarvestReceivingRecord($conn)
    {
        $query = "SELECT unique_member_id, SUM(total_field_size_threshed), SUM(total_field_size_threshed) * 5990 AS cost
                  FROM harvest_receiving_record
                  WHERE threshing_flag = 1
                  GROUP BY unique_member_id";

        $stmt = $conn->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // function fetchManualConfirmThreshingData($conn)
    // {
    //     $query = "SELECT unique_field_id, unique_member_id, SUM(field_size), sum(field_size) * 5990 AS cost, ik_number
    //     FROM public.manual_confirm_threshing
    //     WHERE delete_flag = 0 AND LOWER(type_of_thresher) = 'company' 
    //     GROUP BY unique_field_id, unique_member_id, ik_number";

    //     $stmt = $conn->prepare($query);
    //     $stmt->execute();
    //     return $stmt->fetchAll(PDO::FETCH_ASSOC);
    // }

    // function MergeManualAndConfirmThreshingRecord($confirmThreshingData, $manualconfirmThreshingData)
    // {
    //     $combinedData = array_merge($confirmThreshingData, $manualconfirmThreshingData);

    //     $mergedData = [];

    //     foreach ($combinedData as $data) {
    //         $uniqueMemberId = $data['unique_member_id'];

    //         if (!isset($mergedData[$uniqueMemberId])) {
    //             $mergedData[$uniqueMemberId] = $data; 
    //         }
    //     }

    //     return $mergedData;

    // }
}
