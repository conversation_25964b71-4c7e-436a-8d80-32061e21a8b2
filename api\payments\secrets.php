<?php
require_once (__DIR__.'/../../constants.php');
require_once (__DIR__.'/../../connect_db.php');
require_once (__DIR__.'/../../app/controllers/__payment_validations.php');
require_once (__DIR__.'/../../app/models/payment_secrets.php');
require_once (__DIR__.'/../auth.php');
require_once (__DIR__.'/../../app/models/exception.php');
require_once (__DIR__.'/../../app/models/app_logs.php');
require_once (__DIR__.'/../../app/controllers/cors.php');

cors();



(function(){
try{
    $user = validateUser();
    
    $driver = new DBDriver;
    $con = $driver->connectPgSql(PG_FINANCE_DB_NAME);

    $json = file_get_contents('php://input');
    $data = json_decode($json, true);

    if($_SERVER['REQUEST_METHOD'] == 'GET'){
        $secrets = PaymentSecrets::selectMany($con);
        $response = setResponse(true,"Payments secrets successfully fetched",$secrets,null);
        http_response_code(200);
        echo json_encode($response);
    }


    else if($_SERVER['REQUEST_METHOD'] == 'POST')
    {
    
        if(!isValidSecretpayload($data)) throw new CustomException(400,"One or more parameters are missing or invalid");
        if($data['action'] == 'create'){
        PaymentSecrets::insertOne($con, $data);
        }
        else if($data['action'] == 'update'){
            PaymentSecrets::updateOne($con, $data['key'], ['value'=>$data['value']]);
        }
        $response = setResponse(true,"Payment secret successfully created or updated",[$data['key']=>$data['value']],null);
        http_response_code(200);
        echo json_encode($response);
        AppLogs::insertOne($con,generateLogs($user->data->staff_id,"CREATED/UPDATED SECRET", $data));

    }
    else{
        throw new CustomException(403,"Request Method Not Allowed");
}
}
catch(CustomException $e){
    http_response_code($e->status ?? 500);
    echo json_encode(setResponse(false,"Something went wrong.. ",[],$e->getMessage()));  
}
})()

?>