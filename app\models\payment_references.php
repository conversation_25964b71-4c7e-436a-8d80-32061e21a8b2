<?php

class PaymentReferences
{

    public static function selectMany($con){
        try{
            $stmt = $con->prepare("SELECT group_id,payment_reference,use_status,created_at FROM payment_references");
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            }
            catch(Exception $err){
                throw new Exception("Ooops!!! Looks like we couldn't fetch payment references ". $err->getMessage());
            }
    }
        /* selectOne : Runs a select query on payment_references table for a particular group ID 
         * @param $con : database conection object
         * returns - null
         */
    public static function selectOne($con,$id){
        try{
            $stmt = $con->prepare("SELECT payment_reference FROM payment_references WHERE group_id = ? AND use_status = 0");
            $stmt->execute([$id]);
            $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
            if(count($result) != 1)
            throw new Exception("Either the payee does not have a payment reference or has more than one active reference");
            return array_column($result, 'payment_reference')[0];
            }
            catch(Exception $err){
                throw new Exception("Ooops!!! Looks like we couldn't fetch a payment reference... ". $err->getMessage());
            }
    }
    public static function insertOne($con, $data)
    {
        try {
            $stmt = $con->prepare("INSERT INTO payment_references (group_id,payment_reference) VALUES(?,?)");
            $stmt->execute([$data['id'],$data['reference']]);
        } catch (Exception $err) {
            throw new Exception("Ooops!!! Looks like we couldn't INSERT payment reference - " . $err->getMessage());
        }
    }
}

?>