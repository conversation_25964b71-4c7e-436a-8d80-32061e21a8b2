pipeline {
    agent any


	

    stages {
        
//         stage('Code Quality Check via SonarQube') {
//            steps{
//            script{ 
//           def scannerHome = tool 'sonarqube-scanner';
//           withSonarQubeEnv("babbangona-dev") {
//                sh "${scannerHome}/bin/sonar-scanner -Dsonar.projectKey=collection-center-payment -Dsonar.sources=. -Dsonar.css.node=. -Dsonar.host.url=http://localhost:9000/ -Dsonar.login=**************************************** -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info"
//                }
		   
//              }
//            }
//         }
//         stage('Unit Test'){

//           steps{
//               sh "cd unittest"
//                }
		   
//              }

        stage (' Deploying in Dev Environment: develop') {
            when { branch 'develop' }
            steps {
                echo 'Deploying the application to develop...'
                sshPublisher(publishers: [sshPublisherDesc(configName: 'centos-wp', transfers: [sshTransfer(cleanRemote: false, excludes: '', execCommand: ''' 
                GITUSER=$(cat gituser.txt)
                GITPASSWD=$(cat gittoken.txt)
		USERPASSWD=$(cat password.txt)
                cd /var/www/html/collection-center-dev/collection_center_payment/
                pwd
                hostname
              
               	echo $USERPASSWD | sudo -S git pull https://$GITUSER:$<EMAIL>/BabbanGonaDev/collection_center_payment.git develop
                    
                
                ''', execTimeout: 2000000, flatten: false, makeEmptyDirs: false, noDefaultExcludes: false, patternSeparator: '[, ]+', remoteDirectory: '.', remoteDirectorySDF: false, removePrefix: '', sourceFiles: '*.tar.gz')], usePromotionTimestamp: false, useWorkspaceInPromotion: false, verbose: true)])        
            }
        }

        stage ('Deploying in Prod Environment: production') {
            when { branch 'production'  }
            steps {
                echo 'Deploying the application to production...'
                sshPublisher(publishers: [sshPublisherDesc(configName: 'centos-wp', transfers: [sshTransfer(cleanRemote: false, excludes: '', execCommand: ''' 
                GITUSER=$(cat gituser.txt)
                GITPASSWD=$(cat gittoken.txt)
		USERPASSWD=$(cat password.txt)
                cd /var/www/html/collection_center_payment/
                pwd
                hostname
		echo $USERPASSWD | sudo -S git pull https://$GITUSER:$<EMAIL>/BabbanGonaDev/collection_center_payment.git production

  
		
                ''', execTimeout: 2000000, flatten: false, makeEmptyDirs: false, noDefaultExcludes: false, patternSeparator: '[, ]+', remoteDirectory: '.', remoteDirectorySDF: false, removePrefix: '', sourceFiles: '*.tar.gz')], usePromotionTimestamp: false, useWorkspaceInPromotion: false, verbose: true)])        
            }
        }
    }
}
  
