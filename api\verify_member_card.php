<?php
 
require_once '../app/controllers/cors.php';
require_once '../app/controllers/__card_validations.php';
require_once '../constants.php';
require_once '../connect_db.php';
require_once '../app/models/exception.php';
require_once '../app/models/member_cards.php';
require_once '../app/controllers/__response_helpers.php';
require_once '../app/controllers/__payment_validations.php';

cors();

(function(){
    try{
        $driver = new DBDriver;
        $con = $driver->connectPgSql(PG_FINANCE_DB_NAME);


        if($_SERVER['REQUEST_METHOD'] != 'POST') throw new CustomException(403,"Request Method Not Allowed");
        $json = file_get_contents('php://input');
        $data = json_decode($json, true);

        if(!isValidCardPayload($data)) throw new CustomException(400,"Invalid card payload, one or more parameters are missing or invalid");

        $responseArray = [];

        foreach($data as $one_card){
            $errors = validateMemberCardPayload($one_card);
            if (!empty($errors)) {
                $responseArray[] = [
                    "success" => false,
                    "member_id" => $one_card['id'],
                    "errors" => $errors,
                ];
            }else{
                try {
                    $one_card["status_message"] = "Card verified successfully";
                    MemberCards::insertOne($con, $one_card);
                    $responseArray[] = [
                        "success" => true,
                        "member_id" => $one_card['id'],
                        "message" => "Card verified successfully",
                    ];
                } catch (Exception $arr) {
                    $responseArray[] = [
                        "success" => false,
                        "member_id" => $one_card['id'],
                        "errors" => $arr->getMessage(),
                    ];
                }
            }
        }
        http_response_code(200);
        echo json_encode(setResponse(true,"Card verification request received ",$responseArray, null));
    }
    catch(CustomException $e){
        http_response_code($e->status ?? 500);
        echo json_encode(setResponse(false,"Something went wrong.. ", null, $e->getMessage()));
    }
})()

?>