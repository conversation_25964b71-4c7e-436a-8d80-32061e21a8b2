<?php
require_once(__DIR__ . '/../../constants.php');
require_once(__DIR__ . '/../../connect_db.php');
require_once(__DIR__ . '/../../app/controllers/__payment_validations.php');
require_once(__DIR__ . '/../auth.php');
require_once(__DIR__ . '/../../app/models/exception.php');
require_once(__DIR__ . '/../../app/models/payment_queue.php');
require_once(__DIR__ . '/../../app/controllers/cors.php');
require_once(__DIR__ . '/../../app/models/app_logs.php');

ini_set('max_execution_time', '1000');

cors();

(function () {
    try {
        //echo "Start of the function"."\n";

        $user = validateUser();
        if ($_SERVER['REQUEST_METHOD'] != 'POST') throw new CustomException(403, "Request Method Not Allowed");

        //echo "Validated user and about to connect to GCP"."\n";
        
        $driver = new DBDriver;
        $conn = $driver->connectPgSql(PG_FINANCE_DB_NAME);

        //echo "GCP Connection completed, moving on"."\n";

        $initiator_name = $user->data->staff_name;
        $initiator_role = $user->data->config[0]->permission_name;

        //echo "Extracted initiator name and roles"."\n";

        $successMessages = [];
        $errorMessages = [];

        if ($_FILES['csv_file']['error'] === UPLOAD_ERR_OK) {
            //echo "Start of upload function"."\n";

            $csvFile = $_FILES['csv_file']['tmp_name'];

            //echo "Received CSV File, about to open"."\n";

            if (($handle = fopen($csvFile, "r")) !== false) {

                //echo "Entered IF block"."\n";

                $expectedColumns = ['reason', 'payee_id', 'amount', 'bank_name', 'account_number', 'classification'];
                $csvColumns = fgetcsv($handle);
                $csvColumns = array_map('trim', $csvColumns);

                //echo "Checked for expected columns and map the trims"."\n";

                if ($csvColumns !== $expectedColumns) {
                    throw new CustomException(400, "CSV columns do not match the expected columns or are not in the correct order");
                }

                //echo "This means the expected columns match"."\n";

                if (feof($handle)) {
                    throw new CustomException(400, "The CSV file is empty");
                }

                //echo "This means CSV file isn't empty"."\n";

                $rowCounter = 0;
                while (($data = fgetcsv($handle, 1000, ",")) !== false) {
                    $rowCounter++;

                    if ($rowCounter > 200) {
                        $errorMessages[] = [
                            'data' => 'N/A',
                            'message' => 'CSV file has more than 200 rows. Only the first 200 have been processed.',
                        ];
                        break; //Stop the loop after 200 rows.
                    }

                    //echo "Iterating through the rows ". $data[1]."\n";

                    //Extracting and validating payment data from CSV line
                    $paymentData = [
                        'reason' => $data[0],
                        'payee_id' => $data[1],
                        'amount' => (int)$data[2],
                        'bank_name' => $data[3],
                        'account_number' => $data[4],
                        'classification' => $data[5],
                    ];

                    //Check if classification is valid
                    if (!in_array($paymentData['classification'], VALID_CLASSIFICATIONS)) {
                        //echo "Failed valid classifications check ". $data[1] ." ". $data[5]."\n";

                        $errorMessages[] = [
                            'data' => $data,
                            'message' => 'Invalid classification: ' . $paymentData['classification'] . '. It must exactly match one of the predefined options.',
                        ];
                        continue; 
                    }

                    if (!isValidInstantPaymentPayload($paymentData)) {
                        //echo "Failed valid payload check ". $data[1]."\n";

                        $errorMessages[] = [
                            'data' => $data,
                            'message' => 'Invalid payment payload, one or more parameters are missing or invalid',
                        ];
                    } else {
                        //echo "We are about to insert into payment queue ". $data[1]."\n";
                        try {
                            PaymentQueue::insertInstantPayment($conn, $paymentData, $initiator_name, $initiator_role);
                            $successMessages[] = [
                                'data' => $data,
                                'message' => 'Payment has been successfully queued',
                            ];
                        } catch (CustomException $err) {
                            //echo "Failed to insert into payment queue ". $data[1] ."\n";
                            //var_dump($err);

                            $errorMessages[] = [
                                'data' => $data,
                                'message' => 'Payment not queued: ' . $err->getMessage(),
                            ];
                        }

                        //echo "We completed insert into payment queue successfully ". $data[1]."\n";
                    }
                }
                fclose($handle);

                //echo "Closed the file successfully ". $data[1];
            } else {
                throw new CustomException(400, "Error reading CSV file");
            }
        } else {
            throw new CustomException(400, "Error uploading CSV file");
        }

        http_response_code(201);
        $payment_response = [
            'status' => true,
            'message' => 'Bulk payment request received',
            'success_messages' => $successMessages,
            'error_messages' => $errorMessages,
        ];
        echo json_encode($payment_response);

        AppLogs::insertOne($conn, generateLogs($user->data->staff_id, "SUBMITTED BULK INSTANT PAYMENTS", $successMessages));
    } catch (CustomException $e) {
        http_response_code($e->status ?? 500);
        echo json_encode(setResponse(false, "Something went wrong... Payment not queued ", [], $e->getMessage()));
    }
})();

?>