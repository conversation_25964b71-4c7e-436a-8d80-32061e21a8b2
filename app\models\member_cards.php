<?php
require_once(__DIR__ . "/../controllers/__data_helpers.php");


class MemberCards{
    /* selectPending : Runs a select query on member_cards only selecting  not yet transferred records
         * @param $con : database connection object
         * returns - null
         */
    public static function selectPending($con){
        try{
            $query = "SELECT * FROM member_cards WHERE (recipient_id IS NULL OR NOT recipient_id ILIKE '%RCP%')
                      AND LENGTH(account_number) = 10  AND profiling_attempts < 2 LIMIT 100";
            $stmt = $con->prepare($query);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $err) {
            throw new Exception("Ooops!!! Looks like we couldn't fetch all member records - " . $err->getMessage());
        }
    }

    public static function updateOne($con, $id, $update_pairs)
    {
        $update_string = '';
        try {
            // build up the update string
            $index = 0;
            foreach ($update_pairs as $key => $value) {
                $update_string .= "{$key} ='{$value}'";
                if($index < count($update_pairs) - 1 )
                $update_string .= ",";
                ++$index;
            }
            $stmt = $con->prepare("UPDATE member_cards SET ${update_string} WHERE id = ?");
            $stmt->execute([$id]);
        } catch (Exception $err) {
            throw new Exception("Ooops!!! Looks like we couldn't update all member card records - " . $err->getMessage());
        }
    }

     /* updateMany : updates columns for many records on harvest_member_payments using IN keyword
         * @param $con : database connection object
         * @param $ik_num_array : an array of HSF records
         * @param $update_pairs : an assoc array of column names and the values they should be updated to
         * returns - null
         */
    public static function updateMany($con, $ik_num_array, $update_pairs ){
        $ik_numbers = $ik_num_array;
        $update_string = '';
        try {
            // build up the update string
            foreach ($update_pairs as $key => $value) {
                $update_string .= "{$key} ='{$value}',";
            }
            $chunks = array_chunk($ik_numbers, 65000);
            foreach ($chunks as $eachChunk) {
                $placeholder_string = join(",", array_fill(0, count($eachChunk), "?"));
                $query = "UPDATE harvest_member_payments SET ${update_string} updated_at = NOW() WHERE ik_number IN ({$placeholder_string})";
                $stmt = $con->prepare($query);
                $stmt->execute(array_values($eachChunk));
            }
        } catch (Exception $err) {
            throw new Exception("Ooops!!! Looks like we couldn't update all member records - " . $err->getMessage());
        }
    }
        /* insertMany : inserts records into cleared records table using prepared statements and parameters
         * @param $con : database connection object
         * @param $insertArray : an array of HSF records
         * @param $length : specifies how many records to be inserted at once
         * returns - null
         */
    public static function insertMany($con, $insertArray, $length,$config){
        $member_cards_cols_arr = [
            "id","category","recipient_id", "account_number","account_name","card_number","bank_name","product_code",
            "branch_name","date_updated"
        ];
        $duplicate_string = generateDuplicateStringPG($member_cards_cols_arr, ['id']);
        $member_cards = join(",", $member_cards_cols_arr);

        try{
            $chunks = array_chunk($insertArray, $length );
            foreach($chunks as $eachChunk){
                $placeholder_array = [];
                for($i=0; $i < count($eachChunk); $i++){
                    $placeholder_array[] = "(?,?,?,?,?,?,?,?,?,?,?)";
                }
                $placeholder_string = join(",", $placeholder_array);
                $query = "INSERT INTO member_cards ({$member_cards}) VALUES {$placeholder_string} ON CONFLICT(id) DO UPDATE SET {$duplicate_string}
                ";
                $stmt = $con->prepare($query);
    
                $oneMultiInsertArray = [];
                foreach($eachChunk as $eachRecord){
                    $oneMultiInsertArray[] = $eachRecord["id"]; //  id
                    $oneMultiInsertArray[] = $eachRecord["category"]; // collection_center_id
                    $oneMultiInsertArray[] = $eachRecord["recipient_id"]; // hub id
                    $oneMultiInsertArray[] = $eachRecord["account_number"]; // hub name
                    $oneMultiInsertArray[] = $eachRecord["account_name"]; // unique_member_id
                    $oneMultiInsertArray[] = $eachRecord["card_number"]; //ik_number
                    $oneMultiInsertArray[] = $eachRecord["bank_name"]; // total_weight
                    $oneMultiInsertArray[] = $eachRecord["product_code"]; // product_type
                    $oneMultiInsertArray[] = $eachRecord["branch_name"]; // variety
                    $oneMultiInsertArray[] = $eachRecord["date_updated"]; // variety
                }
                $stmt->execute($oneMultiInsertArray);
            }
        }
        catch(Exception $err){
            throw new Exception("Ooops!!! Looks like we couldn't insert all member card records - ". $err->getMessage());
        }
    }
    public static function selectMany($con){
        $member_cards_cols_arr = [
            "id","category","recipient_id", "account_number","account_name","bank_name","status_message","updated_at","profiling_attempts" 
        ];
        $member_cards_cols = join(",",$member_cards_cols_arr);
        try{
                $stmt = $con->prepare("SELECT ${member_cards_cols} FROM member_cards");
                $stmt->execute();
                return $stmt->fetchAll(PDO::FETCH_ASSOC);
            }
        catch(Exception $err){
            throw new Exception("Ooops!!! Looks like we couldn't select all member card records - ". $err->getMessage());
        }
    }

    public static function selectOne($con, $account_number, $account_name){
        $member_cards_cols_arr = [
            "id","category","recipient_id", "account_number","account_name","bank_name","status_message","updated_at","profiling_attempts" 
        ];
        $member_cards_cols = join(",",$member_cards_cols_arr);
        try{
                $stmt = $con->prepare("SELECT $member_cards_cols FROM member_cards WHERE account_number = ? OR account_name = ?");
                $stmt->execute([$account_number, $account_name]);
                return $stmt->fetch(PDO::FETCH_ASSOC);
            }
        catch(Exception $err){
            throw new Exception("Ooops!!! Looks like we couldn't select a member card record - ". $err->getMessage());
        }
    }

    public static function insertOne($con, $insertArray){
        $data = MemberCards::selectOne($con, $insertArray['account_number'], $insertArray['account_name']);

        if($data){
            if (strtoupper($data['id']) != strtoupper($insertArray['id'])) {
                throw new Exception("Member card already exist and belongs to this account - " . $data['id']);
            }
        }

        $member_cards_cols_arr = [
            "id","category","account_number","account_name","bank_name", "status_message" 
        ];
        $member_cards_cols = join(",",$member_cards_cols_arr);
        $insertArray['id'] = strtoupper($insertArray['id']);
        
        try{
            $query = "INSERT INTO member_cards ({$member_cards_cols}) VALUES (?,?,?,?,?,?) ON CONFLICT((UPPER(id))) DO UPDATE SET category = EXCLUDED.category, account_number = EXCLUDED.account_number, 
            account_name = EXCLUDED.account_name, bank_name = EXCLUDED.bank_name, recipient_id = null, updated_at = now(), profiling_attempts = 0";
            $stmt = $con->prepare($query);
            $values = [
                $insertArray['id'],
                $insertArray['category'],
                $insertArray['account_number'],
                $insertArray['account_name'],
                $insertArray['bank_name'],
                $insertArray['status_message']
            ];
    
            // Execute the statement with the extracted values
            $stmt->execute($values);
        }
        catch(Exception $err){
            throw new Exception("Ooops!!! Looks like we couldn't insert member card record - ". $err->getMessage());
        }
    }
}

?>