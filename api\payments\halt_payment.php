<?php
require_once(__DIR__ . '/../../constants.php');
require_once(__DIR__ . '/../../connect_db.php');
require_once(__DIR__ . '/../auth.php');
require_once(__DIR__ . '/../../app/models/exception.php');
require_once(__DIR__ . '/../../app/models/payment_queue.php');
require_once(__DIR__ . '/../../app/models/app_logs.php');
require_once(__DIR__ . '/../payment_helper.php');
require_once (__DIR__.'/../../app/controllers/cors.php');

cors();

function haltPayment()
{
    try {
        $user = validateUser();

        $driver = new DBDriver;
        $conn = $driver->connectPgSql(PG_FINANCE_DB_NAME);

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            throw new CustomException(403, "Request Method Not Allowed");
        }

        $json = file_get_contents('php://input');
        $data = json_decode($json, true);

        $user_name = $user->data->staff_name;
        $halt_config = false;
        if (isset($user->data->config[0]->configs)) {
            $canApprove = array_filter($user->data->config[0]->configs, function($config) {
                return $config->config_name === 'canHaltPayment' && $config->config_value === true;
            });
    
            if (!empty($canApprove)) {
                $halt_config = true;
            }
        }

        if (!isset($data['reference_ids']) || !is_array($data['reference_ids'])) {
            throw new CustomException(400, "Reference ID(s) missing or not in the correct format");
        }
        
        if (!$halt_config) {
            throw new CustomException(403, "You don't have the permission to halt paymant");
        }      

        $reference_ids_array = '{' . implode(',', $data['reference_ids']) . '}';
        
        // Check if any of the reference IDs have approval_flag = 1
        $stmtCheckApproval = $conn->prepare("SELECT reference FROM payment_queue WHERE reference = ANY(:reference_ids) AND approval_flag = 1");
        $stmtCheckApproval->execute([':reference_ids' => $reference_ids_array]);
        
        $referencesWithApprovalFlag1 = $stmtCheckApproval->fetchAll(PDO::FETCH_COLUMN);
        
        if (!empty($referencesWithApprovalFlag1)) {
            throw new CustomException(403, "Cannot halt payment that has been approved");
        }
        
        $stmtDelete = $conn->prepare("DELETE FROM payment_queue WHERE reference = ANY(:reference_ids) AND approval_flag = 0");
        $stmtDelete->execute([':reference_ids' => $reference_ids_array]);
        
        echo json_encode(['status' => true, 'message' => 'Payment(s) halted successfully!']);
        
        
        AppLogs::insertOne($conn, generateLogs($user_name, "HALTED PAYMENT", json_encode($data)));

    } catch (CustomException $e) {
        http_response_code($e->status ?? 500);
        echo json_encode(['status' => false, 'message' => $e->getMessage()]);
    }
}

haltPayment();

?>