<?php

require_once(__DIR__ . '/../auth.php');
require_once '../../app/controllers/cors.php';
require_once(__DIR__ . '/../../constants.php');
require_once(__DIR__ . '/../../connect_db.php');
require_once(__DIR__ . '/../../app/models/app_logs.php'); 

cors();

function downloadFloatTemplate()
{
    try {
        $user = validateUser();

        $driver = new DBDriver;
        $conn = $driver->connectPgSql(PG_FINANCE_DB_NAME);
        
        $filename = "float_config_template.csv";
        
        $columns = ['id', 'name', 'rate', 'role', 'hub_name', 'opening_balance', 'account_number', 'account_name', 'bank_name', 'collection_center_id'];
        
        $sampleData1 = ['T-****************', '<PERSON>', 200, 'Company Transporter', '<PERSON>kara', 50000, '**********', '<PERSON> <PERSON>', '<PERSON><PERSON>'];
        $sampleData2 = ['T-****************', '<PERSON>', 200, 'Independent Transporter', 'I<PERSON>', 50000, '**********', 'Professor <PERSON><PERSON>e', 'Zenith'];
        $sampleData3 = ['T-****************', 'Professor Dumbledore', 150, 'Labourer', 'Soba', 50000, '**********', 'Professor Dumbledore', 'Access', 'N<PERSON>B201-A01'];

        $tempFile = fopen('php://temp', 'w+');
        
        fputcsv($tempFile, $columns);
        fputcsv($tempFile, $sampleData1);
        fputcsv($tempFile, $sampleData2);
        fputcsv($tempFile, $sampleData3);
        rewind($tempFile);

        header('Content-Type: text/csv');
        header("Content-Disposition: attachment;filename=\"$filename\"");
        fpassthru($tempFile);
        
        fclose($tempFile);

        AppLogs::insertOne($conn, generateLogs($user->data->staff_id, "USER DOWNLOADED FLOAT CONFIG TEMPLATE", $filename));

    } catch (CustomException $e) {
        http_response_code($e->status ?? 500);
        echo json_encode(['status' => false, 'message' => $e->getMessage()]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['status' => false, 'message' => $e->getMessage()]);
    }
}


downloadFloatTemplate();

?>
