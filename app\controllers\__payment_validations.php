<?php
// function isValidPaymentPayload($data)
// {
//     return (!empty($data) && !empty($data['reason']) && !empty($data['payee_id']) && !empty($data['action']) && !empty($data['recipient_id'])
//         && substr($data['recipient_id'], 0, 3) == 'RCP' && !empty($data['amount']) && $data['amount'] > 0 && !empty($data['payment_type'])
//         && in_array($data['payment_type'], ['float_payment', 'aggregate_payment', 'harvest_advance'])
//     );
// }

// function isValidPaymentPayload($data)
// {
//     if (
//         !empty($data) && !empty($data['reason']) && !empty($data['payee_id']) && 
//         !empty($data['action']) && !empty($data['recipient_id']) &&
//         substr($data['recipient_id'], 0, 3) == 'RCP' && !empty($data['amount']) && 
//         $data['amount'] > 0 && !empty($data['payment_type']) && 
//         in_array($data['payment_type'], ['float_payment', 'aggregate_payment', 'harvest_advance'])
//     ) {
//         // Additional check for maximum amount based on payment_type
//         if ($data['payment_type'] === 'harvest_advance' && $data['amount'] > MAX_MEMBER_PAYOUT) {
//             return false;
//         } elseif ($data['payment_type'] === 'float_payment' && $data['amount'] > MAX_FLOAT_PAYOUT) {
//             return false;
//         }
//         return true;
//     }
//     return false;
// }

function validatePaymentPayload($data)
{
    $errors = [];

    if (empty($data)) {
        $errors[] = "Payment data is empty.";
    } else {
        if (empty($data['reason'])) {
            $errors[] = "Reason is missing.";
        }
        if (empty($data['payee_id'])) {
            $errors[] = "Payee ID is missing.";
        }
        if (empty($data['action'])) {
            $errors[] = "Action is missing for {$data['payee_id']}.";
        }
        if (empty($data['recipient_id']) || substr($data['recipient_id'], 0, 3) !== 'RCP') {
            $errors[] = "Invalid recipient ID for {$data['payee_id']}.";
        }
        if (empty($data['amount']) || $data['amount'] <= 0) {
            $errors[] = "Invalid amount for {$data['payee_id']}.";
        }
        if (empty($data['payment_type']) || !in_array($data['payment_type'], ['float_payment', 'aggregate_payment', 'harvest_advance'])) {
            $errors[] = "Invalid payment type for {$data['payee_id']}.";
        }
        // Additional check for maximum amount based on payment_type
        if ($data['payment_type'] === 'harvest_advance' && $data['amount'] > MAX_MEMBER_PAYOUT) {
            $errors[] = "{$data['payee_id']} Amount exceeds maximum for harvest advance.";
        } elseif ($data['payment_type'] === 'float_payment' && $data['amount'] > MAX_FLOAT_PAYOUT) {
            $errors[] = "{$data['payee_id']} Amount exceeds maximum for float payment.";
        }
    }

    return $errors;
}


function isValidInstantPaymentPayload($data)
{
    return (!empty($data) && !empty($data['reason']) && !empty($data['payee_id'])
        && is_int($data['amount']) && $data['amount'] > 0
        && !empty($data['bank_name']) && !empty($data['account_number'])
        && !empty($data['classification'])
    );
}


function getPaymentType($classification)
{
    if (str_contains(strtolower($classification), 'lmr')) return 'wedi_lmr_payment';
    else if (str_contains($classification, 'Float')) return 'float_payment';
    else return 'harvest_advance';
    // return str_contains($classification, 'Float') ? 'float_payment' : 'harvest_advance';
}

function isValidSecretpayload($data)
{
    return (!empty($data) && !empty($data['action']) && in_array($data['action'], ['create', 'update'])
        && !empty($data['key']) && !empty($data['value'])
    );
}

function isValidBulkPayload($data)
{
    return (!empty($data) && !empty($data['bulk']) && !empty($data['data']) && $data['bulk'] == true && gettype($data['data']) == 'array'
    );
}

function isValidReferencepayload($data)
{
    return (!empty($data) && !empty($data['id'])
    );
}

function isValidCardPayload($data)
{
    return (!empty($data) && gettype($data) == 'array'
    );
}
