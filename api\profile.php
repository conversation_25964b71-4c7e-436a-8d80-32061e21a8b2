<?php
require_once '../connect_db.php';
require_once '../constants.php';
require_once '../app/controllers/cors.php';
require_once '../api/auth.php';

cors();

$user = validateUser();

if ($user && isset($user->data)) {
    $staffId = $user->data->staff_id;
    $staffName = $user->data->staff_name;
    $staffdepartment = $user->data->department;
    $staffRole = $user->data->role;

    $staffPicture = "https://storage.googleapis.com/babbangona-prod-bucket-2/staff_pictures/{$staffId}.jpg";

    $mappedConfigs = [];

    if (isset($user->data->config[0]->configs)) {
        foreach ($user->data->config[0]->configs as $config) {
            $mappedConfigs[$config->config_name] = $config->config_value;
        }
    }

    $data = [
        "staff_id" => $staffId,
        "staff_name" => $staffName,
        "staff_department" => $staffdepartment,
        "staff_role" => $staffRole,
        "staff_picture" => $staffPicture,
        "configs" => $mappedConfigs
    ];

    echo json_encode($data, JSON_PRETTY_PRINT);
} else {
    http_response_code(401);
    echo json_encode([
        "error" => "Invalid user or insufficient data."
    ]);
    die();
}

?>
