<?php

require_once(__DIR__ . "/../controllers/__data_helpers.php");

function scalingRecords($conn_inventory, $ik_num)
{
    try {
        $placeholder_string = join(',', array_fill(0,count($ik_num),'?'));

        $query = "SELECT ik_number, scaling_waybill_id FROM harvest_scaling_record WHERE ik_number IN ({$placeholder_string})";
        $stmt = $conn_inventory->prepare($query);
        $stmt->execute(array_values($ik_num));
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Organize the results into the desired format
        $formattedResults = [];
        foreach ($results as $result) {
            $ikNumber = $result['ik_number'];
            $waybillId = $result['scaling_waybill_id'];

            // Check if the entry for this IK number already exists in the formatted array
            if (isset($formattedResults[$ikNumber])) {
                $formattedResults[$ikNumber]['scaling_waybill_id'][] = $waybillId;
            } else {
                $formattedResults[$ikNumber] = [
                    'ik_number' => $ikNumber,
                    'scaling_waybill_id' => [$waybillId],
                ];
            }
        }

        return $formattedResults;
    } catch (Exception $e) {
        throw new Exception("Ooops!!! Looks like we couldn't fetch all Harvest Scaling records - ". $e->getMessage());
    }
}

function receiverRecords($conn_inventory, $ik_num)
{
    try {
        $placeholder_string = join(',', array_fill(0,count($ik_num),'?'));

        $query = "SELECT ik_number, receiving_waybill_id FROM harvest_receiving_record WHERE ik_number IN ({$placeholder_string})";
        $stmt = $conn_inventory->prepare($query);
        $stmt->execute(array_values($ik_num));
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Organize the results into the desired format
        $formattedResults = [];
        foreach ($results as $result) {
            $ikNumber = $result['ik_number'];
            $waybillId = $result['receiving_waybill_id'];

            // Check if the entry for this IK number already exists in the formatted array
            if (isset($formattedResults[$ikNumber])) {
                $formattedResults[$ikNumber]['receiving_waybill_id'][] = $waybillId;
            } else {
                $formattedResults[$ikNumber] = [
                    'ik_number' => $ikNumber,
                    'receiving_waybill_id' => [$waybillId],
                ];
            }
        }

        return $formattedResults;
    } catch (Exception $e) {
        throw new Exception("Ooops!!! Looks like we couldn't fetch all Harvest Receiving records- ". $e->getMessage());
    }
}

function checkerScalerRecords($conn_inventory, $ik_num)
{
    try {
        $placeholder_string = join(',', array_fill(0,count($ik_num),'?'));

        $query = "SELECT ik_number, scaling_waybill_id FROM harvest_checker_scaler_record WHERE ik_number IN ({$placeholder_string})";
        $stmt = $conn_inventory->prepare($query);
        $stmt->execute(array_values($ik_num));
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Organize the results into the desired format
        $formattedResults = [];
        foreach ($results as $result) {
            $ikNumber = $result['ik_number'];
            $waybillId = $result['scaling_waybill_id'];

            // Check if the entry for this IK number already exists in the formatted array
            if (isset($formattedResults[$ikNumber])) {
                $formattedResults[$ikNumber]['scaling_waybill_id'][] = $waybillId;
            } else {
                $formattedResults[$ikNumber] = [
                    'ik_number' => $ikNumber,
                    'scaling_waybill_id' => [$waybillId],
                ];
            }
        }

        return $formattedResults;
    } catch (Exception $e) {
        throw new Exception("Ooops!!! Looks like we couldn't fetch all Harvest Checker Scaler records - ". $e->getMessage());
    }
}

function checkerReceiverRecords($conn_inventory, $ik_num)
{
    try {
        $placeholder_string = join(',', array_fill(0,count($ik_num),'?'));

        $query = "SELECT ik_number, receiving_waybill_id FROM harvest_checker_receiver_record WHERE ik_number IN ({$placeholder_string})";
        $stmt = $conn_inventory->prepare($query);
        $stmt->execute(array_values($ik_num));
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Organize the results into the desired format
        $formattedResults = [];
        foreach ($results as $result) {
            $ikNumber = $result['ik_number'];
            $waybillId = $result['receiving_waybill_id'];

            // Check if the entry for this IK number already exists in the formatted array
            if (isset($formattedResults[$ikNumber])) {
                $formattedResults[$ikNumber]['receiving_waybill_id'][] = $waybillId;
            } else {
                $formattedResults[$ikNumber] = [
                    'ik_number' => $ikNumber,
                    'receiving_waybill_id' => [$waybillId],
                ];
            }
        }

        return $formattedResults;
    } catch (Exception $e) {
        throw new Exception("Ooops!!! Looks like we couldn't fetch all Harvest Checker Receiver records - ". $e->getMessage());
    }
}

function verifierRecords($conn_inventory, $ik_num)
{
    try {
        $placeholder_string = join(',', array_fill(0,count($ik_num),'?'));

        $query = "SELECT s.ik_number, s.scaling_waybill_id FROM harvest_scaling_record s LEFT JOIN harvest_verifier_record v ON s.scaling_waybill_id = v.waybill_id 
        WHERE s.ik_number IN ({$placeholder_string}) AND s.verifier_flag = 1 AND (v.verifier_flag != 1 OR v.waybill_id IS NULL)";
        $stmt = $conn_inventory->prepare($query);
        $stmt->execute(array_values($ik_num));
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Organize the results into the desired format
        $formattedResults = [];
        foreach ($results as $result) {
            $ikNumber = $result['ik_number'];
            $waybillId = $result['scaling_waybill_id'];

            // Check if the entry for this IK number already exists in the formatted array
            if (isset($formattedResults[$ikNumber])) {
                $formattedResults[$ikNumber]['scaling_waybill_id'][] = $waybillId;
            } else {
                $formattedResults[$ikNumber] = [
                    'ik_number' => $ikNumber,
                    'scaling_waybill_id' => [$waybillId],
                ];
            }
        }

        return $formattedResults;
    } catch (Exception $e) {
        throw new Exception("Ooops!!! Looks like we couldn't fetch all Harvest Verifier records - ". $e->getMessage());
    }
}

function clearedRecords($conn_inventory, $ik_num)
{
    try {
        $placeholder_string = join(',', array_fill(0,count($ik_num),'?'));

        $query = "SELECT ik_number, waybill_id FROM harvest_cleared_record WHERE ik_number IN ({$placeholder_string})";
        $stmt = $conn_inventory->prepare($query);
        $stmt->execute(array_values($ik_num));
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Organize the results into the desired format
        $formattedResults = [];
        foreach ($results as $result) {
            $ikNumber = $result['ik_number'];
            $waybillId = $result['waybill_id'];

            // Check if the entry for this IK number already exists in the formatted array
            if (isset($formattedResults[$ikNumber])) {
                $formattedResults[$ikNumber]['waybill_id'][] = $waybillId;
            } else {
                $formattedResults[$ikNumber] = [
                    'ik_number' => $ikNumber,
                    'waybill_id' => [$waybillId],
                ];
            }
        }

        return $formattedResults;
    } catch (Exception $e) {
        throw new Exception("Ooops!!! Looks like we couldn't fetch all Harvest Cleared records - ". $e->getMessage());
    }
}

function getTotalBags($conn_inventory, $ik_num)
{
    try {
        $placeholder_string = join(',', array_fill(0,count($ik_num),'?'));

        $query = "SELECT s.ik_number, COALESCE(SUM(s.bags_marketed), 0) AS scaler, COALESCE(SUM(r.bags_received), 0) AS receiver, COALESCE(SUM(cs.bags_marketed), 0) AS checker_scaler,
        COALESCE(SUM(cr.bags_received), 0) AS checker_receiver, COALESCE(SUM(c.bags_marketed), 0) AS cleared
        FROM harvest_scaling_record s
        LEFT JOIN harvest_receiving_record r on s.scaling_waybill_id = r.scaling_waybill_id
        LEFT JOIN harvest_checker_scaler_record cs on s.scaling_waybill_id = cs.scaling_waybill_id
        LEFT JOIN harvest_checker_receiver_record cr on s.receiving_waybill_ids = cr.receiving_waybill_id
        LEFT JOIN harvest_cleared_record c on s.scaling_waybill_id = c.waybill_id
        WHERE s.ik_number IN ({$placeholder_string})
        GROUP BY s.ik_number";

        $stmt = $conn_inventory->prepare($query);
        $stmt->execute(array_values($ik_num));
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return $results;
    } catch (Exception $e) {
        throw new Exception("Ooops!!! Looks like we couldn't fetch all Total Bags records - ". $e->getMessage());
    }
}

function getActiveMembers($conn_rec, $ik_num)
{
    try {
        $tg_arr = getActiveTG($ik_num);
        $placeholder_string = join(',', array_fill(0,count($tg_arr),'?'));
        if (empty($tg_arr)) return [];

        $query = "SELECT m.ik_number, CONCAT('R23-',m.ik_number,'-',m.member_id) AS member_id, CONCAT(m.first_name, ' ', m.last_name) AS member_name, m.unique_member_id FROM members_entity m JOIN clearance_members c ON m.unique_member_id = c.unique_member_id WHERE m.ik_number IN ({$placeholder_string}) AND c.clearance_field_size > '0'";
        $stmt = $conn_rec->prepare($query);
        $stmt->execute(array_values($tg_arr));
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $formattedResults = [];

        foreach ($results as $result) {
            $ikNumber = $result['ik_number'];

            if (!isset($formattedResults[$ikNumber])) {
                $formattedResults[$ikNumber] = [
                    'ik_number' => $ikNumber,
                    'members' => [],
                ];
            }

            $formattedResults[$ikNumber]['members'][] = [
                'member_id' => $result['member_id'],
                'member_name' => $result['member_name'],
                'unique_member_id' => $result['unique_member_id'],
            ];
        }

        return $formattedResults;

    } catch (Exception $e) {
        throw new Exception("Ooops!!! Looks like we couldn't fetch all Active Members records - ". $e->getMessage());
    }
}

function getTGWaybillRecords($conn_inventory, $ik_num)
{
    try {
        $placeholder_string = join(',', array_fill(0,count($ik_num),'?'));

        $query = "SELECT s.ik_number, s.scaling_waybill_id, r.receiving_waybill_id, s.unique_member_id FROM harvest_scaling_record s LEFT JOIN harvest_receiving_record r ON s.scaling_waybill_id = r.scaling_waybill_id WHERE s.ik_number IN ({$placeholder_string})";
        $stmt = $conn_inventory->prepare($query);
        $stmt->execute(array_values($ik_num));
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Organize the results into the desired format
        $formattedResults = [];
        foreach ($results as $result) {
            $ikNumber = $result['ik_number'];
            $waybillId = $result['scaling_waybill_id'];
            $receivingWaybillIds = $result['receiving_waybill_id'];
            $uniqueMemberId = $result['unique_member_id'];

            // Check if the entry for this IK number already exists in the formatted array
            if (isset($formattedResults[$ikNumber])) {
                $formattedResults[$ikNumber]['scaling_waybill_id'][] = $waybillId;
                $formattedResults[$ikNumber]['receiving_waybill_id'][] = $receivingWaybillIds;
                $formattedResults[$ikNumber]['unique_member_id'][] = $uniqueMemberId;
            } else {
                $formattedResults[$ikNumber] = [
                    'ik_number' => $ikNumber,
                    'scaling_waybill_id' => [$waybillId],
                    'receiving_waybill_id' => [$receivingWaybillIds],
                    'unique_member_id' => [$uniqueMemberId],
                ];
            }
        }

        return $formattedResults;
    } catch (Exception $e) {
        throw new Exception("Ooops!!! Looks like we couldn't fetch all Harvest Scaling records - ". $e->getMessage());
    }
}

function getTGLeader($arr){
    try {
        $driver = new DBDriver;
        $conn_rec = $driver->connectPgSql(PG_RECRUITMENT_DB_NAME);
        $conn_planning = $driver->connectPgSql(PG_PLANNING_DB_NAME);

        $tg_arr = getActiveTG($arr);

        if (empty($tg_arr)) return [];
        
        $placeholder_string = join(",", array_fill(0,count($tg_arr), "?"));
        $query = "SELECT m.unique_member_id AS member_id, m.ik_number, concat(m.first_name, ' ', m.last_name) AS payee_name, m.phone_number, m.community_name, m.role, m.hub_id ::integer FROM members_entity m JOIN clearance_members c ON m.unique_member_id = c.unique_member_id WHERE m.ik_number IN ({$placeholder_string}) AND m.role = 'Leader' AND c.clearance_field_size > '0'";
        $stmt = $conn_rec->prepare($query);
        $stmt->execute(array_values($tg_arr));
        $leader = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $hub_ids = array_column($leader, 'hub_id');
        $hubs = getTGHubs($conn_planning, $hub_ids);

        $indexed_hubs = [];
        foreach ($hubs as $hub) {
            $indexed_hubs[$hub['hub_id']] = $hub['hub_name'];
        }

        foreach ($leader as $key => $value) {
            if (isset($value['hub_id'])) {
                $leader[$key]['hub_name'] = $indexed_hubs[$value['hub_id']];
            }
        }

        return $leader;
    } catch (PDOException $e) {
        throw new Exception("Ooops!!! Looks like we couldn't fetch all TG Leader records - ". $e->getMessage());
    }
}

// function getActiveTrustGroups ($conn_finance)
// {
//     try {
//         $query = "SELECT ik_number FROM harvest_trust_group_payments";

//         $stmt = $conn_finance->prepare($query);
//         $stmt->execute();
//         $ik_num = $stmt->fetchAll(PDO::FETCH_COLUMN);

//         $activeTgs = getActiveTG($ik_num);
//         return $activeTgs;
//     } catch (Exception $e) {
//         throw new Exception("Ooops!!! Looks like we couldn't fetch all TG Member Payment records - ". $e->getMessage());
//     }
// }

function tgPayDiagnostic($conn_finance, $conn_inventory, $conn_rec, $ik_num)
{
    try {
        // $tg_arr = getActiveTrustGroups($conn_finance);
        // $placeholder_string = join(',', array_fill(0,count($tg_arr),'?'));
        $query = "SELECT t.ik_number, COALESCE(h.amount_paid, 0) AS amount_paid, t.net_harvest_advance, m.recipient_id,
        CASE
            WHEN ABS(t.net_harvest_advance - COALESCE(h.amount_paid, 0)) <= 100 AND COALESCE(h.amount_paid, 0) > 0 THEN 'Paid'
            WHEN COALESCE(h.amount_paid, 0) > t.net_harvest_advance + 100 AND t.net_harvest_advance > 0 THEN 'Over paid'
            WHEN t.net_harvest_advance > COALESCE(h.amount_paid, 0) + 100 AND COALESCE(h.amount_paid, 0) > 0 THEN 'Underpaid'
            WHEN COALESCE(h.amount_paid, 0) = 0 AND t.net_harvest_advance > 0 THEN 'Not Paid'
            ELSE 'None'
        END AS payment_status,
        -- CASE
        --     WHEN t.shp_dnp_override = 1 THEN 'Shp Dnp Override'
        --     WHEN (t.finance_dnp = 1 AND t.shp_dnp = 1) OR t.finance_dnp = 1 THEN 'Finance Dnp'
        --     WHEN t.shp_dnp = 1 THEN 'Shp Dnp'
        --     ELSE 'None'
        -- END AS payment_risk
        CASE
            WHEN (t.finance_dnp = 1 AND t.finance_dnp_override = 1) 
            OR (t.shp_dnp = 1 AND t.shp_dnp_override = 1) 
            OR (t.tech_dnp = 1 AND t.tech_dnp_override = 1) THEN 'DNP Override'
            WHEN t.finance_dnp = 1 AND t.finance_dnp_override = 0 THEN 'Finance Dnp'
            WHEN t.shp_dnp = 1 AND t.shp_dnp_override = 0 THEN 'Shp Dnp'
            WHEN t.tech_dnp = 1 AND t.tech_dnp_override = 0 THEN 'Tech Dnp'
            ELSE 'None'
        END AS payment_risk
        FROM harvest_trust_group_payments t
        LEFT JOIN member_cards m ON t.ik_number = m.id -- Fetched recipient_id from member cards rather than harvest transactions to validate if a member card is verified or not
        LEFT JOIN (SELECT payee_id, SUM(amount) as amount_paid, recipient_id FROM harvest_transactions WHERE payment_type = 'harvest_advance' GROUP BY payee_id, recipient_id) h ON t.ik_number = h.payee_id
        WHERE t.ik_number = ?";

        $stmt = $conn_finance->prepare($query);
        $stmt->execute([$ik_num]);
        $tgs = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $ik_num = array_column($tgs, 'ik_number');

        $bags = getTotalBags($conn_inventory, $ik_num);
        $scalers = scalingRecords($conn_inventory, $ik_num);
        $members = getActiveMembers($conn_rec, $ik_num);
        $receivers = receiverRecords($conn_inventory, $ik_num);
        $checkers = checkerScalerRecords($conn_inventory, $ik_num);
        $checker_receivers = checkerReceiverRecords($conn_inventory, $ik_num);
        $verifiers = verifierRecords($conn_inventory, $ik_num);
        $cleared = clearedRecords($conn_inventory, $ik_num);
        $tg_waybill = getTGWaybillRecords($conn_inventory, $ik_num);
        $tg_leader = getTGLeader($ik_num);

        $waybillByMember = [];
        foreach ($tg_waybill as $ikNumber => $data) {
            $uniqueMemberIds = $data['unique_member_id'];
            foreach ($uniqueMemberIds as $index => $uniqueMemberId) {
                $waybillByMember[$uniqueMemberId][] = [
                    'scaling_waybill_id' => $data['scaling_waybill_id'][$index],
                    'receiving_waybill_id' => $data['receiving_waybill_id'][$index],
                ];
            }
        }

        foreach ($members as $ikNumber => &$memberData) {
            foreach ($memberData['members'] as &$member) {
                $uniqueMemberId = $member['unique_member_id'];
                if (isset($waybillByMember[$uniqueMemberId])) {
                    // Add the waybill info to the member
                    $member['waybills'] = $waybillByMember[$uniqueMemberId];
                } else {
                    // If no waybill data is found, you can decide what to do
                    $member['waybills'][] = [
                        'scaling_waybill_id' => null,
                        'receiving_waybill_id' => null,
                    ];
                }
            }
        }
        // Unset reference to avoid unintended side effects
        unset($memberData, $member);
        
        $indexed_bags = array_column($bags, null, 'ik_number');
        $indexed_scaler = array_column($scalers, 'scaling_waybill_id', 'ik_number');
        $indexed_member = array_column($members, null, 'ik_number');
        $indexed_receiver = array_column($receivers, 'receiving_waybill_id', 'ik_number');
        $indexed_checker = array_column($checkers, 'scaling_waybill_id', 'ik_number');
        $indexed_checker_receiver = array_column($checker_receivers, 'receiving_waybill_id', 'ik_number');
        $indexed_verifier = array_column($verifiers, 'scaling_waybill_id', 'ik_number');
        $indexed_cleared = array_column($cleared, 'waybill_id', 'ik_number');
        $indexed_tg_leader = array_column($tg_leader, null, 'ik_number');

        $result = [];
        foreach ($tgs as $tg) {
            $ikN = $tg['ik_number'];
            $merged_item = $tg + [
                'bags' => isset($indexed_bags[$ikN]) ? array_diff_key($indexed_bags[$ikN], ['ik_number' => true]) : [
                    "scaler" => 0,
                    "receiver" => 0,
                    "checker_scaler" => 0,
                    "checker_receiver" => 0,
                    "cleared" => 0,
                ],
                'waybills' => [
                    'scaler' => isset($indexed_scaler[$ikN]) ? count($indexed_scaler[$ikN]) : 0,
                    'receiver' => isset($indexed_receiver[$ikN]) ? count($indexed_receiver[$ikN]) : 0,
                    'checker_scaler' => isset($indexed_checker[$ikN]) ? count($indexed_checker[$ikN]) : 0,
                    'checker_receiver' => isset($indexed_checker_receiver[$ikN]) ? count($indexed_checker_receiver[$ikN]) : 0,
                    'verifier' => isset($indexed_verifier[$ikN]) ? count($indexed_verifier[$ikN]) : 0,
                    'cleared' => isset($indexed_cleared[$ikN]) ? count($indexed_cleared[$ikN]) : 0,
                ],
                // 'scaling_waybill_ids' => $indexed_scaler[$ikN] ?? [],
                // 'receiving_waybill_ids' => $indexed_receiver[$ikN] ?? [],
                // 'checker_scaler_waybill_ids' => $indexed_checker[$ikN] ?? [],
                // 'checker_receiver_waybill_ids' => $indexed_checker_receiver[$ikN] ?? [],
                // 'verifier_waybill_ids' => $indexed_verifier[$ikN] ?? [],
                // 'cleared_waybill_ids' => $indexed_cleared[$ikN] ?? [],
                'members' => $indexed_member[$ikN]['members'] ?? [],
                'tg_leader' => $indexed_tg_leader[$ikN] ?? (object)[],
            ];

            $result = $merged_item;
        }

        return $result;

    } catch (Exception $e) {
        throw new Exception("Ooops!!! Looks like we couldn't fetch all TG Member Payment records - ". $e->getMessage());
    }
}

function getTgPayDiagnostic($conn_finance, $conn_inventory, $id = null)
{
    try {
        $query = "SELECT t.ik_number, t.net_harvest_advance, t.no_of_bags_marketed AS tg_bags,
        CASE
            WHEN ABS(t.net_harvest_advance - COALESCE(h.amount_paid, 0)) <= 100 AND COALESCE(h.amount_paid, 0) > 0 THEN 'Paid'
            WHEN COALESCE(h.amount_paid, 0) > t.net_harvest_advance + 100 AND t.net_harvest_advance > 0 THEN 'Over paid'
            WHEN t.net_harvest_advance > COALESCE(h.amount_paid, 0) + 100 AND COALESCE(h.amount_paid, 0) > 0 THEN 'Underpaid'
            WHEN COALESCE(h.amount_paid, 0) = 0 AND t.net_harvest_advance > 0 THEN 'Not Paid'
            ELSE 'None'
        END AS payment_status
        FROM harvest_trust_group_payments t
        LEFT JOIN (SELECT payee_id, SUM(amount) as amount_paid, recipient_id FROM harvest_transactions WHERE payment_type = 'harvest_advance' GROUP BY payee_id, recipient_id) h ON t.ik_number = h.payee_id
        WHERE (t.net_harvest_advance::integer > 0) 
        AND t.ik_number NOT IN (SELECT DISTINCT payee_id FROM payment_queue WHERE payment_category = 'harvest_advance') AND (t.payment_ready + t.all_flags_override::integer) > 0";

        if ($id) {
            $cleanIds = preg_replace("/['\"]/", "", $id);
            $idArray = array_map('trim', explode(',', $cleanIds));
            $placeholders = implode(',', array_fill(0, count($idArray), '?'));
            $query .= " AND t.ik_number IN ($placeholders)";
        }

        $stmt = $conn_finance->prepare($query);

        if ($id) {
            $stmt->execute($idArray);
        } else {
            $stmt->execute();
        }

        $tgs = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Handle case where no records are found
        if (empty($tgs)) {
            return [];
        }

        $ik_num = array_column($tgs, 'ik_number');

        $bags = getTotalBags($conn_inventory, $ik_num);
        $indexed_bags = array_column($bags, null, 'ik_number');

        $result = [];
        foreach ($tgs as $tg) {
            $ikN = $tg['ik_number'];
            $merged_item = $tg + [
                'bags' => isset($indexed_bags[$ikN]) ? array_diff_key($indexed_bags[$ikN], ['ik_number' => true]) : [
                    "scaler" => 0,
                    "receiver" => 0,
                    "checker_scaler" => 0,
                    "checker_receiver" => 0,
                    "cleared" => 0,
                ],
            ];

            $result[] = $merged_item;
        }

        return $result;

    } catch (Exception $e) {
        throw new Exception("Ooops!!! Looks like we couldn't fetch all TG Member Payment records - " . $e->getMessage());
    }
}


?>
