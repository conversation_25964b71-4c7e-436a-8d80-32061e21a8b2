<?php

require_once(__DIR__ . '/../auth.php');
require_once '../../app/controllers/cors.php';
require_once(__DIR__ . '/../../constants.php');
require_once(__DIR__ . '/../../connect_db.php');
require_once(__DIR__ . '/../../app/models/app_logs.php'); 

cors();

function downloadInstantPayTemplate()
{
    try {
        $user = validateUser();

        $driver = new DBDriver;
        $conn = $driver->connectPgSql(PG_FINANCE_DB_NAME);
        
        $filename = "instant_pay_template.csv";
        
        $columns = ['reason', 'payee_id', 'amount', 'bank_name', 'account_number', 'classification'];

        $sampleData1 = ['Payment has been delayed for a month', 'T-****************', 2000, 'Access Bank', '**********', 'Emergency Labour Float'];
        $sampleData2 = ['Payment has been delayed for 3 months', 'T-****************', 15000, 'Access Bank', '**********', 'Emergency Transportation Float'];
        $sampleData3 = ['Share of operator profit', 'T-****************', 2000, 'Access Bank', '**********', 'Operator Profit Share'];
        $sampleData4 = ['Upfront payment to member', 'T-****************', 15000, 'Access Bank', '**********', 'Upfront Refund'];
        $sampleData5 = ['TGE Bonus for **********', 'T-****************', 90000, 'Access Bank', '***********', 'TGE Bonus'];

        $tempFile = fopen('php://temp', 'w+');
        
        fputcsv($tempFile, $columns);
        fputcsv($tempFile, $sampleData1);
        fputcsv($tempFile, $sampleData2);
        fputcsv($tempFile, $sampleData3);
        fputcsv($tempFile, $sampleData4);
        fputcsv($tempFile, $sampleData5);
        rewind($tempFile);

        header('Content-Type: text/csv');
        header("Content-Disposition: attachment;filename=\"$filename\"");
        fpassthru($tempFile);
        
        fclose($tempFile);

        AppLogs::insertOne($conn, generateLogs($user->data->staff_id, "USER DOWNLOADED INSTANT PAY TEMPLATE", $filename));

    } catch (CustomException $e) {
        http_response_code($e->status ?? 500);
        echo json_encode(['status' => false, 'message' => $e->getMessage()]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['status' => false, 'message' => $e->getMessage()]);
    }
}


downloadInstantPayTemplate();

?>
