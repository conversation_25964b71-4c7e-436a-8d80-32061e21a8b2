<?php

function get_float_payment($id = null)
{
    try {
        $driver = new DBDriver;
        $conn_finance = $driver->connectPgSql(PG_FINANCE_DB_NAME);

        //Excluded records on the payment queue
        $sql = $sql = "
        SELECT 
        fr.id, 
        fr.name, 
        fr.role, 
        fr.hub, 
        fr.opening_balance, 
        fr.recipient_id, 
        fr.no_of_bags, 
        fr.cost_per_bag, 
        fr.total_value, 
        fr.amount_paid, 
        fr.amount_to_be_paid, 
        fr.payment_status, 
        fr.payment_status_name, 
        fpc.freeze_flag,
        fpc.account_number,
        fpc.account_name,
        fpc.bank_name
    FROM 
        float_records fr
    LEFT JOIN 
        float_payment_config fpc ON fr.id = fpc.id
    WHERE 
        fr.id NOT IN (SELECT DISTINCT payee_id FROM payment_queue)
    AND 
        fr.no_of_bags > 0
    AND 
        fr.role LIKE '%Transporter%'
    ";

        if ($id) {
            $cleanIds = preg_replace("/['\"]/", "", $id);
            $idArray = array_map('trim', explode(',', $cleanIds));
            $placeholders = implode(',', array_fill(0, count($idArray), '?'));
            $sql .= " AND fr.id IN ($placeholders)";
        }
        $stmt = $conn_finance->prepare($sql);
        // $stmt->execute();
        if ($id) {
            $stmt->execute($idArray);
        } else {
            $stmt->execute();
        }

        $result = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if ($result) {
            return [
                'success' => true,
                'data' => $result,
                'message' => 'Float Records retrieved successfully',
            ];
        } else {
            return [
                'success' => false,
                'data' => [],
                'message' => 'No records found',
            ];
        }
    } catch (PDOException $e) {
        return [
            'success' => false,
            'data' => [],
            'message' => 'Error: ' . $e->getMessage(),
        ];
    }
}


function get_soy_payment()
{
    try {
        $driver = new DBDriver;
        $conn_finance = $driver->connectPgSql(PG_FINANCE_DB_NAME);
        $conn_inventory = $driver->connectPgSql(PG_INVENTORY_DB_NAME);

        $configDbOperations = new ConfigDbOperations($conn_inventory);
        $config = $configDbOperations->get_data();

        $sql = "SELECT payee_id, SUM(amount) as amount_paid  FROM harvest_transactions WHERE payment_type = 'aggregate_payment' GROUP BY payee_id ";

        $stmt = $conn_finance->prepare($sql);
        $paid_result = array();
        if ($stmt->execute()) {
            while ($data = $stmt->fetch()) {
                $key = $data["payee_id"];
                $paid_result[$key] = $data["amount_paid"];
            }

            $sql = "SELECT a.unique_member_id, a.ik_number, SUM(a.no_of_bags_marketed) as bags_marketed, SUM(a.net_weight_marketed) as net_weight, a.variety, b.recipient_id FROM harvest_member_payments_soy a JOIN member_cards b ON a.ik_number = b.id WHERE  a.ik_number not IN (SELECT DISTINCT payee_id FROM payment_queue) GROUP BY unique_member_id, ik_number, variety, recipient_id";

            $stmt = $conn_finance->prepare($sql);
            $cc_result_payment = array();
            if ($stmt->execute()) {
                while ($data = $stmt->fetch()) {
                    $variety = $data["variety"];
                    $unique_member_id = $data["unique_member_id"];
                    $ik_number = $data["ik_number"];
                    $bags_marketed = $data["bags_marketed"];
                    $net_weight = $data["net_weight"];
                    $recipient_id = $data["recipient_id"];
                    $price_per_kg = 0;

                    if (strtolower($variety) == "silver") {
                        $price_per_kg = $config->silver_soy;
                    } elseif (strtolower($variety) == "mai_idon_fara") {
                        $price_per_kg = $config->mai_idon_fara_soy;
                    }

                    $harvest_member = $cc_result_payment[$unique_member_id] ?? array();
                    $harvest_member["unique_member_id"] = $unique_member_id;
                    $harvest_member["ik_number"] = $ik_number;
                    $harvest_member["bags_marketed"] = $bags_marketed;
                    $harvest_member["net_weight"] = $net_weight;
                    $harvest_member["amount_to_be_paid"] = ($harvest_member["amount_to_be_paid"] ?? 0) + $price_per_kg * $net_weight;
                    $harvest_member["recipient_id"] = $recipient_id;

                    $cc_result_payment[$unique_member_id] = $harvest_member;
                }

                $result = array();
                foreach ($cc_result_payment as $member) {
                    $data = array();
                    $total_value = round($member["amount_to_be_paid"], 2);
                    $amount_paid = doubleval($paid_result[$member["unique_member_id"]] ?? 0);
                    $data["unique_member_id"] = $member["unique_member_id"];
                    $data["recipient_id"] = $member["recipient_id"];
                    $data["ik_number"] = $member["ik_number"];
                    $data["bags_marketed"] = $member["bags_marketed"];
                    $data["net_weight"] = doubleval($member["net_weight"]);
                    $data["total_value"] = $total_value;
                    $data["amount_paid"] = $amount_paid;
                    $data["amount_to_be_paid"] = round($total_value - $amount_paid, 2);
                    $data["payment_status"] = get_payment_status($total_value, $amount_paid);
                    $data["payment_status_name"] = get_payment_status_name($data["payment_status"]);

                    $result[] = $data;
                }

                $response = get_response(true, $result, null);
                $code = 200;
            } else {
                $response = get_response(false, "Unable to add configuration", $stmt->errorInfo());
                $code = 400;
            }
        } else {
            $response = get_response(false, "Unable to add configuration", $stmt->errorInfo());
            $code = 400;
        }
    } catch (PDOException $exception) {
        $response = get_response(false, "Unable to get data", $exception->getMessage());
        $code = 400;
    }

    http_response_code($code);

    return $response;
}

// check for if harvest personnel has been freezed
function getFloatFreezeStatus($conn, $payee_id)
{
    $sql = "SELECT freeze_flag FROM float_payment_config WHERE id = :payee_id LIMIT 1";
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':payee_id', $payee_id, PDO::PARAM_INT);

    if ($stmt->execute()) {
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($result !== false && isset($result['freeze_flag'])) {
            return $result;
        }
    }
    return null;
}



function get_response($status, $message, $error)
{
    return [
        'success' => $status,
        'data' => $message,
        'error' => $error
    ];
}

function filter_unpaid($row)
{
    return $row['payment_status'] == -1 || $row['payment_status'] == 0;
}

function filter_paid($row)
{
    return $row['payment_status'] == 1 || $row['payment_status'] == 2;
}

function payout($conn)
{
    $query = "SELECT value FROM collection_center_config WHERE config_id IN ('max_payout_config_maize', 'min_payout_config_maize')";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    return $results;
}

/**
 * @param $response
 * @param string $filename
 * @return void
 */
function extract_payment_file($response, string $filename): void
{

    $type = "all";
    if (isset($_GET['type'])) {
        $type = $_GET['type'];
    }

    $keys = array_keys($response[0]);


    if ($type == "unpaid") {
        $response = array_filter($response, 'filter_unpaid');
    } elseif ($type == "paid") {
        $response = array_filter($response, 'filter_paid');
    }

    //header info for browser
    header("Content-Type: text/csv");
    header("Content-Disposition: attachment; filename=$filename.csv");
    header("Pragma: no-cache");
    header("Expires: 0");
    /*******Start of Formatting for Excel*******/
    //define separator (defines columns in excel & tabs in word)
    // $sep = "\t"; //tabbed character
    // //start of printing column names as names of MySQL fields

    // foreach ($keys as $key) {
    //     echo $key . "\t";
    // }

    // print("\n");
    // //end of printing column names
    // //start while loop to get data
    // foreach ($response as $row) {
    //     $schema_insert = "";
    //     foreach ($keys as $j) {
    //         if (!isset($row[$j]))
    //             $schema_insert .= "NULL" . $sep;
    //         elseif ($row[$j] != "")
    //             $schema_insert .= "$row[$j]" . $sep;
    //         else
    //             $schema_insert .= "" . $sep;
    //     }
    //     $schema_insert = str_replace($sep . "$", "", $schema_insert);
    //     $schema_insert = preg_replace("/\r\n|\n\r|\n|\r/", " ", $schema_insert);
    //     $schema_insert .= "\t";
    //     print(trim($schema_insert));
    //     print "\n";
    $output = fopen('php://output', 'w');

    // Extract the column headers from the first row
    $keys = array_keys($response[0]);

    // Output the column headers
    fputcsv($output, $keys);

    // Output data rows
    foreach ($response as $row) {
        fputcsv($output, $row);
    }

    // Close the output stream
    fclose($output);
}

/**
 * @param $response
 * @param string $filename
 * @return void
 */

function extract_trust_group_file($response, string $filename): void
{

    // Set the HTTP headers for CSV download
    header('Content-Type: text/csv');
    header("Content-Disposition: attachment; filename=$filename.csv");
    header('Pragma: no-cache');
    header('Expires: 0');

    $statusMap = [
        0 => "Met Financial",
        1 => "Met Contractual",
        2 => "Over Marketed",
    ];

    $riskMap = [
        0 => "None",
        1 => "DNP Override",
        2 => "Finance DNP",
        3 => "Shp DNP",
    ];

    $paymentStatusMap = [
        0 => "Not Paid",
        1 => "Paid",
        2 => "Overpaid",
        -1 => "Underpaid",
    ];

    // Create an output stream
    $output = fopen('php://output', 'w');

    // Extract the column headers from the first row
    $keys = array_keys($response[0]);

    // Output the column headers
    fputcsv($output, $keys);

    // Output data rows
    foreach ($response as $row) {
        if (isset($row['status']) && array_key_exists($row['status'], $statusMap)) {
            $row['status'] = $statusMap[$row['status']];
        }

        if (isset($row['payment_risk']) && array_key_exists($row['payment_risk'], $riskMap)) {
            $row['payment_risk'] = $riskMap[$row['payment_risk']];
        }

        if (isset($row['payment_status']) && array_key_exists($row['payment_status'], $paymentStatusMap)) {
            $row['payment_status'] = $paymentStatusMap[$row['payment_status']];
        }

        fputcsv($output, $row);
    }

    // Close the output stream
    fclose($output);
}
