<?php
require_once(__DIR__ . '/../api/auth.php');
require_once '../app/controllers/cors.php';
require_once(__DIR__ . '/../constants.php');
require_once(__DIR__ . '/../connect_db.php');
require_once(__DIR__ . '/../app/models/app_logs.php');
require_once(__DIR__.'/../app/models/exception.php' );

cors();

function downloadTransactions()
{
    try {
        $user = validateUser();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            throw new CustomException(405, "Method Not Allowed. Only POST requests are allowed.");
        }

        $input = json_decode(file_get_contents('php://input'), true);
        
        //Check if the required parameters are set
        $id = $input['id'] ?? null;

        $driver = new DBDriver;
        $conn = $driver->connectPgSql(PG_FINANCE_DB_NAME);

        $columns = ['reference', 'payee_id', 'recipient_id', 'payment_type', 'amount',  'status', 'code', 'transaction_date', 'description', 'batch_id', 'account_number', 'account_name', 'bank_name'];

        $queryColumns = ['a.reference', 'a.payee_id', 'a.recipient_id', 'a.payment_type', 'a.amount', 'a.status', 'a.code', 'a.transaction_date', 'a.description', 'a.batch_id', 'b.account_number', 'b.account_name', 'b.bank_name'];

        date_default_timezone_set('Africa/Lagos');
        $dates = date("Y-m-d H:i:s"); // Year-Month-Day Hour:Minute:Second
        $tableName = 'harvest_transactions';
        $filename = $tableName . "-" . $dates;
        header('Content-Type: text/csv');
        header("Content-Disposition: attachment;filename=\"{$filename}.csv\"");

        $fp = fopen('php://output', 'w');

        fputcsv($fp, $columns);

        $sql = "SELECT " . implode(", ", $queryColumns) . " FROM harvest_transactions a LEFT JOIN paystack_recipient_id b ON a.recipient_id = b.recipient_id ORDER BY a.transaction_date DESC";
        if ($id) {
            $cleanIds = preg_replace("/['\"]/", "", $id);
            $idArray = array_map('trim', explode(',', $cleanIds));
            $placeholders = implode(',', array_fill(0, count($idArray), '?'));
            $sql = "SELECT " . implode(", ", $queryColumns) . " 
            FROM harvest_transactions a 
            LEFT JOIN paystack_recipient_id b 
            ON a.recipient_id = b.recipient_id 
            WHERE a.reference IN ($placeholders) 
            ORDER BY a.transaction_date DESC";
        }

        $stmt = $conn->prepare($sql);

        if ($id) {
            $stmt->execute($idArray);
        } else {
            $stmt->execute();
        }

        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            fputcsv($fp, $row);
        }

        fclose($fp);

        // Logging the user's action
        AppLogs::insertOne($conn, generateLogs($user->data->staff_id, "USER DOWNLOADED HARVEST TRANSACTIONS", ""));
    } catch (CustomException $e) {
        http_response_code($e->status ?? 500);
        echo json_encode(['status' => false, 'message' => $e->getMessage()]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['status' => false, 'message' => $e->getMessage()]);
    }
}


downloadTransactions();

?>
