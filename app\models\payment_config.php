<?php
class PaymentConfig{
        /* selectPending : Runs a select query on payment_config only selecting particular configs 
         * @param $con : database connection object
         * @param $category : a string of particular payment config
         * returns - array
         */
    public static function selectOne($con,$type){
        try{
            $stmt = $con->prepare("SELECT pay_until,repeats_every,time_of_payment,payment_flag FROM payment_portal_config WHERE type = ? LIMIT 1");
            $stmt->execute([$type]);
            $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
            if(count($result) == 0) return ["payment_flag"=>0];
            return $result[0];

        }
        catch(Exception $e){
            throw new Exception("Oops!!! We ran into a problem fetching config values. ". $e->getMessage());
        }
    
}
}

?>