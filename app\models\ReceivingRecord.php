<?php

class ReceivingRecord
{
    /**
     * @var string
     */
    private $receiving_waybill_id;

    /**
     * @var string
     */
    private $threshing_flag;

    /**
     * @var string
     */
    private $unique_member_id;

    /**
     * @var string
     */
    private $transportation_flag;

    /**
     * @var string
     */
    private $total_field_size_threshed;

    /**
     * @var string
     */
    private $bags_received;

    /**
     * @var string
     */
    private $transport_info_id;

    /**
     * @var string
     */
    private $transaction_date;

    /**
     * @var string
     */
    private $scaling_waybill_id;

     /**
     * @var string
     */
    private $threshing_field_id;

    /**
     * @return string|null
     */
    public function getReceivingWayBillId()
    {
        return $this->receiving_waybill_id;
    }
    public function getDateReceived()
    {
        return $this->transaction_date;
    }

    public function getUniqueMemberId()
    {
        return $this->unique_member_id;
    }

    public function getThreshingFieldId()
    {
        return $this->threshing_field_id;
    }

    public function getTransportInfoId()
    {
        return $this->transport_info_id;
    }

    /**
     * @return string|null
     */
    public function getThreshingFlag()
    {
        return $this->threshing_flag;
    }

    /**
     * @return string|null
     */
    public function getScalingWaybillId()
    {
        return $this->scaling_waybill_id;
    }

    /**
     * @return string|null
     */
    public function getTransportationFlag()
    {
        return $this->transportation_flag;
    }

    /**
     * @return string|null
     */
    public function getTotalFieldSizeThreshed()
    {
        return $this->total_field_size_threshed;
    }

    /**
     * @return string|null
     */
    public function getBagsReceived()
    {
        return $this->bags_received;
    }
}

