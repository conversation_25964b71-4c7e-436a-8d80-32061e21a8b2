<?php
require_once '../connect_db.php';
require_once '../constants.php';
require_once '../app/controllers/cors.php';
require_once ('auth.php');

cors();

$user = validateUser();
$json = file_get_contents('php://input');
$data = json_decode($json, true);

$payment_flag = $data["payment_flag"] ?? 0;
$type = $data["type"] ?? 0;

if (!in_array($type, ["float_payment", "tg_harvest_payment", "aggregate_payment"])) {
    $response = get_response(false, "Please provide a valid config type", null);
    $code = 400;
} elseif (!in_array($payment_flag, array(0, 1))) {
    $response = get_response(false, "Flag has to be 0 or 1", null);
    $code = 400;
} else {
    try {
        $driver = new DBDriver;
        $conn_inventory = $driver->connectPgSql(PG_FINANCE_DB_NAME);

        $sql = "UPDATE payment_portal_config SET payment_flag = ? WHERE type = ?";

        $stmt = $conn_inventory->prepare($sql);

        $result = $stmt->execute([$payment_flag, $type]);

        if ($result) {
            $response = get_response(true, "Configuration has been successfully updated", null);
            $code = 200;
        } else {
            $response = get_response(false, "Unable to update configuration", $stmt->errorInfo());
            $code = 400;
        }
    } catch (PDOException $exception) {
        $response = get_response(false, "Unable to update configuration", $exception->getMessage());
        $code = 400;
    }

}


http_response_code($code);
echo json_encode($response);

function get_response($status, $message, $error)
{
    return [
        'success' => $status,
        'message' => $message,
        'error' => $error
    ];
}

?>