<?php
// function get_payment_status($total_paid, $amount_paid)
// {
//     if ($total_paid == $amount_paid) {
//         return 1;
//     } elseif ($amount_paid == 0) {
//         return 0;
//     } elseif ($total_paid < $amount_paid) {
//         return 2;
//     } else {
//         return -1;
//     }
// }

// function get_payment_status_name($status)
// {
//     switch ($status) {
//         case 1:
//             return "PAID";
//         case 0:
//             return "UNPAID";
//         case 2:
//             return "OVERPAID";
//         default:
//             return "PARTIAL";
//     }
// }

function get_payment_status($total_paid, $amount_paid)
{
    if ($amount_paid == 0 || $total_paid > $amount_paid) {
        return 0; 
    } elseif ($total_paid == $amount_paid) {
        return 1;
    } elseif ($total_paid < $amount_paid) {
        return 2;
    } 
    return 0; 
}

function get_payment_status_name($status)
{
    switch ($status) {
        case 1:
            return "PAID";
        case 2:
            return "OVERPAID";
        default:
            return "UNPAID";
    }
}
