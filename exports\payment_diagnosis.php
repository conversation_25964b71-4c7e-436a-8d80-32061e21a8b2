<?php
require_once(__DIR__ . '/../api/auth.php');
require_once(__DIR__ . '/../app/controllers/cors.php');
require_once(__DIR__ . '/../constants.php');
require_once(__DIR__ . '/../connect_db.php');
require_once(__DIR__ . '/../app/models/app_logs.php');
require_once(__DIR__ . '/../app/models/payment_diagnosis.php');
require_once(__DIR__.'/../app/models/exception.php' );

cors();

function exportTgPayDiagnostic()
{
    try {
        $user = validateUser();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            throw new CustomException(405, "Method Not Allowed. Only POST requests are allowed.");
        }

        $input = json_decode(file_get_contents('php://input'), true);
        
        //Check if the required parameters are set
        $id = $input['id'] ?? null;

        $driver = new DBDriver;
        $conn_finance = $driver->connectPgSql(PG_FINANCE_DB_NAME);
        $conn_inventory = $driver->connectPgSql(PG_INVENTORY_DB_NAME);

        $columns = ['ik_number', 'receiver', 'scaler', 'checker_receiver', 'checker_scaler', 'cleared', 'tg_bags', 'tg_amount', 'payment_status'];

        date_default_timezone_set('Africa/Lagos');
        $dates = date("Y-m-d H:i:s"); // Year-Month-Day Hour:Minute:Second
        $tableName = 'payment_diagnosis';
        $filename = $tableName . "-" . $dates;
        header('Content-Type: text/csv');
        header("Content-Disposition: attachment;filename=\"{$filename}.csv\"");

        $fp = fopen('php://output', 'w');

        fputcsv($fp, $columns);

        $response = getTgPayDiagnostic($conn_finance, $conn_inventory, $id);

        foreach ($response as $row) {
            // Open the rsponse in append mode ('a')
            $fp = fopen('php://output', 'a');
    
            // Write values into the columns
            $rowData = [
                $row['ik_number'],
                $row['bags']['receiver'],
                $row['bags']['scaler'],
                $row['bags']['checker_receiver'],
                $row['bags']['checker_scaler'],
                $row['bags']['cleared'],
                $row['tg_bags'],
                $row['net_harvest_advance'],
                $row['payment_status'],
            ];
    
            // Write the data to the CSV file
            fputcsv($fp, $rowData);
    
            // Close the output stream
            fclose($fp);
        }

        // Logging the user's action
        AppLogs::insertOne($conn_finance, generateLogs($user->data->staff_id, "USER EXPORTED ALL TG PAYMENT DIAGNOSIS", ""));
    } catch (CustomException $e) {
        http_response_code($e->status ?? 500);
        echo json_encode(['status' => false, 'message' => $e->getMessage()]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['status' => false, 'message' => $e->getMessage()]);
    }
}


exportTgPayDiagnostic();

?>
