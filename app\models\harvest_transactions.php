<?php
require_once(__DIR__ . "/../controllers/__data_helpers.php");


class HarvestTransactions{
    public static function insertOne($con, $insertArray){
        $harvest_transactions_cols_arr = [   
            'reference','payee_id','recipient_id','payment_type','amount','code','transaction_date','description', 'batch_id'
        ];
        $insert_cols = join(",", $harvest_transactions_cols_arr);
        try{
                $stmt = $con->prepare("INSERT INTO harvest_transactions ({$insert_cols}) VALUES (?,?,?,?,?,?,?,?,?)");
                $stmt->execute(array_values($insertArray));
            }
        
        catch(Exception $err){
            throw new Exception("Ooops!!! Looks like we couldn't insert all transactions records - ". $err->getMessage());
        }
    }
    public static function selectMany($con){
        try{
                $stmt = $con->prepare("SELECT a.reference, a.payee_id, a.recipient_id, a.payment_type, a.amount, a.code, a.transaction_date, a.description, a.batch_id, b.account_number, b.account_name, b.bank_name, fp.name as payee_name
                FROM harvest_transactions a
                LEFT JOIN paystack_recipient_id b
                ON a.recipient_id = b.recipient_id
                LEFT JOIN float_payment_config fp
                ON a.payee_id = fp.id
                order by transaction_date DESC");
                $stmt->execute();
                return $stmt->fetchAll(PDO::FETCH_ASSOC);
            }
        catch(Exception $err){
            throw new Exception("Ooops!!! Looks like we couldn't select all completed transactions - ". $err->getMessage());
        }
    }
    public static function selectTGTransactions($con, $insertArray){
        try{
                $stmt = $con->prepare("SELECT payee_id,payee_id, SUM(amount) FROM harvest_transactions GROUP BY payee_id HAVING payment_type = 'harvest_advance'");
                $stmt->execute();
                return $stmt->fetchAll(PDO::FETCH_ASSOC);
            }
        catch(Exception $err){
            throw new Exception("Ooops!!! Looks like we couldn't select all completed transactions - ". $err->getMessage());
        }
    }

    // public static function selectCustom($con, $cols_arr, $where_arr){
             
    //      try {
    //         $cols = join(",", $cols_arr);
    //         $query = "SELECT {$cols} FROM harvest_transactions ";
    //         $where_clause = '';

    //           if(gettype($where_arr) == 'array'){
    //             $index = 0;
    //             // build up where clause
    //             foreach ($where_arr as $key => $value) {
    //                 $where_clause .= "{$key} ='{$value}'";
    //                 if($index < count($where_arr) - 1 )
    //                 $update_string .= " AND";
    //                 ++$index;
    //             }  
    //                 }

    //             if(!empty($where_clause)) $query .= "WHERE {$where_clause}";
    //             echo $query; exit;
    //             $stmt = $con->prepare($query);
    //             $stmt->execute();
    //             return $stmt->fetchAll(PDO::FETCH_CLASS | PDO::FETCH_UNIQUE);
    //         }
    //     catch(Exception $err){
    //         throw new Exception("Ooops!!! Looks like we couldn't select all completed transactions - ". $err->getMessage());
    //     }
    // }

    public static function updateOne($con, $id, $update_pairs)
    {
        $update_string = '';
        try {
            // build up the update string
            $index = 0;
            foreach ($update_pairs as $key => $value) {
                $update_string .= "{$key} ='{$value}'";
                if($index < count($update_pairs) - 1)
                $update_string .= ",";
                ++$index;
            }
            $stmt = $con->prepare("UPDATE harvest_transactions SET {$update_string}  WHERE id = ?");
            $stmt->execute([$id]);
        } catch (Exception $err) {
            throw new Exception("Ooops!!! Looks like we couldn't update all transaction  records - " . $err->getMessage());
        }
    }


    
        /* insertMany : inserts records into cleared records table using prepared statements and parameters
         * @param $con : database connection object
         * @param $insertArray : an array of HSF records
         * @param $length : specifies how many records to be inserted at once
         * returns - null
         */
    public static function insertMany($con, $insertArray, $length,$config){
        $harvest_transactions_cols_arr = [
            "id","category","recipient_id", "account_number","account_name","card_number","bank_name","product_code",
            "branch_name","date_updated"
        ];
        $duplicate_string = generateDuplicateStringPG($harvest_transactions_cols_arr, ['id']);
        $harvest_transactions = join(",", $harvest_transactions_cols_arr);

        try{
            $chunks = array_chunk($insertArray, $length );
            foreach($chunks as $eachChunk){
                $placeholder_array = [];
                for($i=0; $i < count($eachChunk); $i++){
                    $placeholder_array[] = "(?,?,?,?,?,?,?,?,?,?,?)";
                }
                $placeholder_string = join(",", $placeholder_array);
                $query = "INSERT INTO harvest_transactions ({$harvest_transactions}) VALUES {$placeholder_string} ON CONFLICT(id) DO UPDATE SET {$duplicate_string}
                ";
                $stmt = $con->prepare($query);
    
                $oneMultiInsertArray = [];
                foreach($eachChunk as $eachRecord){
                    $oneMultiInsertArray[] = $eachRecord["id"]; //  id
                    $oneMultiInsertArray[] = $eachRecord["category"]; // collection_center_id
                    $oneMultiInsertArray[] = $eachRecord["recipient_id"]; // hub id
                    $oneMultiInsertArray[] = $eachRecord["account_number"]; // hub name
                    $oneMultiInsertArray[] = $eachRecord["account_name"]; // unique_member_id
                    $oneMultiInsertArray[] = $eachRecord["card_number"]; //ik_number
                    $oneMultiInsertArray[] = $eachRecord["bank_name"]; // total_weight
                    $oneMultiInsertArray[] = $eachRecord["product_code"]; // product_type
                    $oneMultiInsertArray[] = $eachRecord["branch_name"]; // variety
                    $oneMultiInsertArray[] = $eachRecord["date_updated"]; // variety
                }
                $stmt->execute($oneMultiInsertArray);
            }
        }
        catch(Exception $err){
            throw new Exception("Ooops!!! Looks like we couldn't insert all transactions records - ". $err->getMessage());
        }
    }

    public static function getHarvestTransactions($conn) {
        $transactions = self::selectMany($conn);
        
        // Filter transactions where payee_name is null
        // $filtered_transactions = array_filter($transactions, function($transaction) {
        //     return is_null($transaction['payee_name']);
        // });
        $filtered_transactions = array_filter($transactions, function($transaction) {
            return preg_match('/^IK\d{8}$/', $transaction['payee_id']);
        });
    
        $payee_ids = array_column($filtered_transactions, 'payee_id');
    
        $tg_leaders = getTGLeaders($payee_ids);
    
        $indexed_tg_leaders = [];
        foreach ($tg_leaders as $leader) {
            // $indexed_tg_leaders[$leader['ik_number']] = $leader['payee_name'];
            $indexed_tg_leaders[$leader['ik_number']] = [
                'payee_name' => $leader['payee_name'],
                'unique_member_id' => $leader['member_id'],
            ];
        }
    
        foreach ($transactions as &$transaction) {
            // if (is_null($transaction['payee_name']) && isset($indexed_tg_leaders[$transaction['payee_id']])) {
            //     $transaction['payee_name'] = $indexed_tg_leaders[$transaction['payee_id']];
            // }
            if (preg_match('/^IK\d{8}$/', $transaction['payee_id'])) {
                if (isset($indexed_tg_leaders[$transaction['payee_id']])) {
                    // Overwrite the payee_name with the one from TG leaders
                    $transaction['payee_name'] = $indexed_tg_leaders[$transaction['payee_id']]['payee_name'];
    
                    // Add the unique_member_id
                    $transaction['unique_member_id'] = $indexed_tg_leaders[$transaction['payee_id']]['unique_member_id'];
                } else {
                    // If no match, set unique_member_id to null
                    $transaction['unique_member_id'] = null;
                }
            } else {
                // If no match, set unique_member_id to null
                $transaction['unique_member_id'] = null;
            }
        }

        return $transactions;
    }
    
}

?>