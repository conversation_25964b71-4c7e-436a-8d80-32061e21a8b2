<?php

require_once(__DIR__ . "/../../app/controllers/__array_key_concat.php");
require_once(__DIR__ . "/../../constants.php");
require_once(__DIR__ . "/../controllers/__data_helpers.php");


class HarvestMemberPaymentUpdate
{

    private ?PDO $connection;

    function __construct($connection)
    {
        $this->connection = $connection;
    }

    public function get_data($season): array
    {
        $query = "select * from public.harvest_member_payments_update where updated_flag = 0 and season = '{$season}'";

        $stmt = $this->connection->prepare($query);

        $stmt->execute();

        $result = array();

        while ($data = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $result[get_key_concat($data["unique_member_id"], $data["product"])] = $data;
        }
        return $result;
    }

    public function get_harvest_advance($ikNumbers): array
    {
        $ikNumbers_str = join(",", $ikNumbers);
        $query = "select * from public.harvest_member_payments_update where ik_number in ( {$ikNumbers_str} )";

        $stmt = $this->connection->prepare($query);

        $stmt->execute();

        $result = array();

        while ($data = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $result[] = $data;
        }
        return $result;
    }

    public function insert_many($records, $chunk_size)
    {
        $records_cols_arr = [
            "unique_member_id", "ik_number", "product", "season", "field_size", "member_status", "percentage_ownership",
            "no_of_bags_marketed", "updated_no_of_bags_marketed", "net_weight_marketed","updated_net_harvest_advance", "grain_value",
            "harvest_advance", "loan_before_harvest", "net_harvest_advance",
            "misc_account", "threshing_cost", "transport_cost", "processing_cost", "total_cost", "shared_debt",
            "payment_ready_date", "updated_flag", "contractual_flag","exceeded_expectation", "created_at", "updated_at", "app_version", "imei", "sync_flag", "hub_name"
        ];

        $duplicate_string = generateDuplicateStringPG($records_cols_arr, ['unique_member_id', 'product', 'season']);

        $record_cols_str = join(",", $records_cols_arr);

        try {
            $chunks = array_chunk($records, $chunk_size);
            foreach ($chunks as $eachChunk) {
                $placeholder_array = [];
                for ($i = 0; $i < count($eachChunk); $i++) {
                    $placeholder_array[] = "(" . "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?" . ")";
                }
                $placeholder_string = join(",", $placeholder_array);
                $query = "INSERT INTO public.harvest_member_payments_update ({$record_cols_str}) VALUES {$placeholder_string} ON CONFLICT(unique_member_id,product,season) DO UPDATE SET {$duplicate_string}";
                // echo $query;
                $stmt = $this->connection->prepare($query);

                $oneMultiInsertArray = [];

                foreach ($eachChunk as $data) {
                    $oneMultiInsertArray[] = $data["unique_member_id"];
                    $oneMultiInsertArray[] = $data["ik_number"];
                    $oneMultiInsertArray[] = $data["product"];
                    $oneMultiInsertArray[] = $data["season"];
                    $oneMultiInsertArray[] = $data["field_size"];
                    $oneMultiInsertArray[] = $data["member_status"];
                    $oneMultiInsertArray[] = $data["percentage_ownership"];
                    $oneMultiInsertArray[] = $data["updated_no_of_bags_marketed"];
                    $oneMultiInsertArray[] = $data["no_of_bags_marketed"];
                    $oneMultiInsertArray[] = $data["net_weight_marketed"];
                    $oneMultiInsertArray[] = $data["updated_net_harvest_advance"];
                    $oneMultiInsertArray[] = $data["grain_value"];
                    $oneMultiInsertArray[] = $data["harvest_advance"];
                    $oneMultiInsertArray[] = $data["loan_before_harvest"];
                    $oneMultiInsertArray[] = $data["net_harvest_advance"];
                    $oneMultiInsertArray[] = $data["misc_account"];
                    $oneMultiInsertArray[] = $data["threshing_cost"];
                    $oneMultiInsertArray[] = $data["transport_cost"];
                    $oneMultiInsertArray[] = $data["processing_cost"];
                    $oneMultiInsertArray[] = $data["total_cost"];
                    $oneMultiInsertArray[] = $data["shared_debt"];
                    $oneMultiInsertArray[] = $data["payment_ready_date"];
                    $oneMultiInsertArray[] = 1;
                    $oneMultiInsertArray[] = $data["contractual_flag"];
                    $oneMultiInsertArray[] = $data["exceeded_expectation"];
                    $oneMultiInsertArray[] = $data["created_at"]; // created_at;
                    $oneMultiInsertArray[] = date("Y-m-d H:i:s"); // updated_at;
                    $oneMultiInsertArray[] = $data["app_version"];
                    $oneMultiInsertArray[] = $data["imei"];
                    $oneMultiInsertArray[] = $data["sync_flag"];
                    $oneMultiInsertArray[] = $data["hub_name"];
                }
                $stmt->execute($oneMultiInsertArray);
            }
        } catch (Exception $err) {
            return "Ooops!!! Looks like we couldn't insert all cleared records - " . $err->getMessage();
        }
        return true;
    }


    public function reset_update_flag(): bool
    {
        $query = "UPDATE public.harvest_member_payments_update SET updated_flag = 0 WHERE
                                                            ik_number in (
                                                                SELECT ik_number from harvest_member_payments_update where updated_flag = 0
                                                            )";
        $stmt = $this->connection->prepare($query);

        $stmt->execute();

        return true;
    }


}