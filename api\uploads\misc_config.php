<?php
require_once(__DIR__ . '/../../constants.php');
require_once(__DIR__ . '/../../connect_db.php');
require_once(__DIR__ . '/../../api/auth.php');
require_once(__DIR__ . '/../../app/models/app_logs.php');
require_once(__DIR__ . '/../../app/models/exception.php');
require_once(__DIR__ . '/../../app/controllers/cors.php');
require_once(__DIR__ . '/../../app/models/app_logs.php');

cors();

function getDbConnection()
{
    $driver = new DBDriver;
    return $driver->connectPgSql(PG_FINANCE_DB_NAME);
}

function uploadMiscConfig($handle, $conn)
{
    $stmt = $conn->prepare(
        "INSERT INTO cc_farm_loans 
        (unique_member_id, product, loan_before_harvest, misc_account, misc_acct_explanation, date_updated)
         VALUES (:unique_member_id, :product, :loan_before_harvest, :misc_account, :misc_acct_explanation, :date_updated)"
    );

    rewind($handle);
    
    fgetcsv($handle);
    while (($data = fgetcsv($handle)) !== FALSE) {
        try {
            $currentTime = date('Y-m-d H:i:s');
            $data[] = $currentTime; // Adding the current time to the end of the data array

            $stmt->bindParam(':unique_member_id', $data[0]);
            $stmt->bindParam(':product', $data[1]);
            $stmt->bindParam(':loan_before_harvest', $data[2], PDO::PARAM_INT);
            $stmt->bindParam(':misc_account', $data[3], PDO::PARAM_STR);
            $stmt->bindParam(':misc_acct_explanation', $data[4], PDO::PARAM_STR);
            $stmt->bindParam(':date_updated', $data[5], PDO::PARAM_STR);

            $stmt->execute();
        } catch (PDOException $e) {
            if ($e->getCode() === '23505') {
                echo "Duplicate entry detected for unique_member_id: {$data[0]} and product: {$data[1]}. Skipping this record.\n";
            } else {
                echo "An unexpected error occurred: " . $e->getMessage() . ". Skipping this record.\n";
            }
        }
    }

    $stmt = null;
    fclose($handle);
}

function main()
{
    try {
        $user = validateUser();

        if ($_SERVER['REQUEST_METHOD'] == 'GET' && isset($_GET['checkUploadStatus']) && $_GET['checkUploadStatus'] === 'true') {
            checkUploadStatus();
            return;
        }

        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            throw new CustomException(403, "Request Method Not Allowed");
        }

        $fileArray = $_FILES['file'] ?? [];
        if (empty($fileArray)) {
            throw new CustomException(400, "Please upload a valid CSV file for payment");
        }

        $fileExt = strtolower(pathinfo($fileArray['name'], PATHINFO_EXTENSION));
        if ($fileExt !== 'csv') {
            throw new CustomException(400, "Please upload a valid CSV file for payment");
        }

        $fileContent = $fileArray['tmp_name'];
        $conn = getDbConnection();

        if (($handle = fopen($fileContent, "r")) !== FALSE) {
            $expectedColumns = ['unique_member_id', 'product', 'loan_before_harvest', 'misc_account', 'misc_acct_explanation'];
            $csvColumns = fgetcsv($handle);
            $csvColumns = array_map('trim', $csvColumns);
            if ($csvColumns !== $expectedColumns) {
                throw new CustomException(400, "CSV columns do not match the expected columns or are not in the correct order");
            }

            $dataRow = fgetcsv($handle);
            if (feof($handle) || $dataRow === false) {
                throw new CustomException(400, "The CSV file is empty");
            }

            uploadMiscConfig($handle, $conn);

            $response = setResponse(true, "Data uploaded successfully", null, null);
            http_response_code(200);
            echo json_encode($response);
            AppLogs::insertOne($conn, generateLogs($user->data->staff_id, "USER UPLOADED MISCELLANEOUS CONFIG", $fileArray['name']));
        }
    } catch (CustomException $e) {
        http_response_code($e->status ?? 500);
        echo json_encode(setResponse(false, "Something went wrong..... ", [], $e->getMessage()));
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(setResponse(false, "An unexpected error occurred", [], $e->getMessage()));
    }
}

function checkUploadStatus()
{
    try {
        $conn = getDbConnection();
        $stmt = $conn->prepare("SELECT COUNT(*) FROM cc_farm_loans");
        $stmt->execute();
        $count = $stmt->fetchColumn();

        http_response_code(200);
        echo json_encode(['status' => true, 'data' => ['tableEmpty' => $count == 0]]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['status' => false, 'message' => $e->getMessage()]);
    }
}

main();
?>