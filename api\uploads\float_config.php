<?php
require_once(__DIR__ . '/../../constants.php');
require_once(__DIR__ . '/../../connect_db.php');
require_once(__DIR__ . '/../../api/auth.php');
require_once(__DIR__ . '/../../app/models/app_logs.php');
require_once(__DIR__ . '/../../app/models/exception.php');
require_once(__DIR__ . '/../../app/controllers/cors.php');
require_once(__DIR__ . '/../../app/models/app_logs.php');

cors();

function getDbConnection()
{
    $driver = new DBDriver;
    return $driver->connectPgSql(PG_FINANCE_DB_NAME);
}

function uploadData($handle, $conn)
{
    $query = "
        INSERT INTO float_payment_config (id, name, rate, role, hub_name, opening_balance, account_number, account_name, bank_name, collection_center_id, created_at, updated_at, freeze_flag) 
        VALUES (:id, :name, :rate, :role, :hub_name, :opening_balance, :account_number, :account_name, :bank_name, :collection_center_id, :created_at, :updated_at, :freeze_flag) 
        ON CONFLICT (id) 
        DO UPDATE SET 
            name = EXCLUDED.name, 
            rate = EXCLUDED.rate, 
            role = EXCLUDED.role, 
            hub_name = EXCLUDED.hub_name, 
            opening_balance = EXCLUDED.opening_balance, 
            account_number = EXCLUDED.account_number, 
            account_name = EXCLUDED.account_name, 
            bank_name = EXCLUDED.bank_name, 
            collection_center_id = EXCLUDED.collection_center_id,
            updated_at = EXCLUDED.updated_at, 
            freeze_flag = EXCLUDED.freeze_flag";
    $stmt = $conn->prepare($query);

    rewind($handle);
    
    fgetcsv($handle);

    while (($dataRow = fgetcsv($handle)) !== FALSE) {

        $currentTime = date('Y-m-d H:i:s');

        $paymentData = [
            ':id' => $dataRow[0],
            ':name' => $dataRow[1],
            ':rate' => $dataRow[2],
            ':role' => isset($dataRow[3]) ? trim($dataRow[3]) : '',
            ':hub_name' => $dataRow[4],
            ':opening_balance' => $dataRow[5],
            ':account_number' => $dataRow[6],
            ':account_name' => $dataRow[7],
            ':bank_name' => $dataRow[8],
            ':collection_center_id' => $dataRow[9],
            ':created_at' => $currentTime,
            ':updated_at' => $currentTime,
            ':freeze_flag' => 0,
        ];

        $allowedRoles = ['Company Transporter', 'Independent Transporter', 'Labourer'];
        if (!in_array($paymentData[':role'], $allowedRoles)) {
            throw new CustomException(400, "Invalid role format. Role should be either 'Company Transporter', 'Independent Transporter' or 'Labourer'");
        }

        try {
            $stmt->execute($paymentData);
        } catch (PDOException $e) {
            throw new CustomException(500, "Database error: " . $e->getMessage());
        }
    }
    fclose($handle);
}



function main()
{
    try {

        $user = validateUser();

        if ($_SERVER['REQUEST_METHOD'] == 'GET' && isset($_GET['checkUploadStatus']) && $_GET['checkUploadStatus'] === 'true') {
            checkUploadStatus();
            return;
        }


        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            throw new CustomException(403, "Request Method Not Allowed");
        }

        $fileArray = $_FILES['file'] ?? [];
        if (empty($fileArray)) {
            throw new CustomException(400, "Please upload a valid CSV file for payment");
        }

        $fileExt = strtolower(pathinfo($fileArray['name'], PATHINFO_EXTENSION));
        if ($fileExt !== 'csv') {
            throw new CustomException(400, "Please upload a valid CSV file for payment");
        }

        $fileContent = $fileArray['tmp_name'];
        $conn = getDbConnection();

        if (($handle = fopen($fileContent, "r")) !== FALSE) {
            $expectedColumns = ['id', 'name', 'rate', 'role', 'hub_name', 'opening_balance', 'account_number', 'account_name', 'bank_name', 'collection_center_id'];
            $csvColumns = fgetcsv($handle);
            $csvColumns = array_map('trim', $csvColumns);
            if ($csvColumns !== $expectedColumns) {
                throw new CustomException(400, "CSV columns do not match the expected columns or are not in the correct order");
            }

            $dataRow = fgetcsv($handle);
            if (feof($handle) || $dataRow === false) {
                throw new CustomException(400, "The CSV file is empty");
            }

            uploadData($handle, $conn);

            $response = setResponse(true, "Data uploaded successfully", null, null);
            http_response_code(200);
            echo json_encode($response);
            AppLogs::insertOne($conn, generateLogs($user->data->staff_id, "USER UPLOADED FLOAT CONFIG", $fileArray['name']));
        }
    } catch (CustomException $e) {
        http_response_code($e->status ?? 500);
        echo json_encode(setResponse(false, "Something went wrong..... ", [], $e->getMessage()));
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(setResponse(false, "An unexpected error occurred", [], $e->getMessage()));
    }
}

function checkUploadStatus()
{
    try {
        $conn = getDbConnection();
        $stmt = $conn->prepare("SELECT COUNT(*) FROM float_payment_config");
        $stmt->execute();
        $count = $stmt->fetchColumn();

        http_response_code(200);
        echo json_encode(['status' => true, 'data' => ['tableEmpty' => $count == 0]]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['status' => false, 'message' => $e->getMessage()]);
    }
}


main();

?>