<?php

require_once '../connect_db.php';
require_once '../constants.php';
require_once '../app/controllers/cors.php';
require_once '../app/controllers/__data_helpers.php';
require_once(__DIR__ . '/../app/models/exception.php');
require_once(__DIR__ . '/../app/models/payment_diagnosis.php');
require_once('auth.php');

cors();

$user = validateUser();

$ik_num = $_GET['ik_number']; 

if (empty($ik_num)) {
    http_response_code(400);
    echo json_encode(setResponse(false, "Missing ik_number parameter", null, null));
    exit;
}

$driver = new DBDriver;
$conn_finance = $driver->connectPgSql(PG_FINANCE_DB_NAME);
$conn_inventory = $driver->connectPgSql(PG_INVENTORY_DB_NAME);
$conn_rec = $driver->connectPgSql(PG_RECRUITMENT_DB_NAME);

$response = tgPayDiagnostic($conn_finance, $conn_inventory, $conn_rec, $ik_num);

http_response_code(200);
echo json_encode(setResponse(true," TG Payment Diagnostic Data fetched successfully ",$response, null));

?>
