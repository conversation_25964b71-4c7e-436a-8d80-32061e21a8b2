<?php

ini_set('memory_limit', '2048M');
ini_set('max_execution_time', '1000');

require_once(__DIR__ . '/../connect_db.php');
require_once(__DIR__ . '/../constants.php');
require_once(__DIR__ . '/../app/controllers/payment_status.php');
require_once(__DIR__ . "/../app/controllers/Logger.php");
require_once(__DIR__ . '/../app/models/exception.php');


$driver = new DBDriver;
$conn_finance = $driver->connectPgSql(PG_FINANCE_DB_NAME);
$conn_transport = $driver->connectPgSql(PG_TRANSPORTATION_DB_NAME);
$conn_inventory = $driver->connectPgSql(PG_INVENTORY_DB_NAME);

function update_transporter_id($conn_inventory)
{
    $query = "UPDATE transport_controller
    SET transporter_id = CONCAT(transporter_id, '_0000')
    WHERE transporter_id ILIKE 'IK%' AND LENGTH(transporter_id) = 10";

    $stmt = $conn_inventory->prepare($query);
    $stmt->execute();
    $results = $stmt->rowCount(); //Return no of rows that were updated.

    return $results;
}

function insert_into_float_payment_config($conn_finance, $processed_results)
{
    $sql = " INSERT INTO float_payment_config
    (id, name, rate, hub_name, opening_balance, account_number, account_name, bank_name, freeze_flag, created_at, updated_at, role, recipient_id, collection_center_id, status_message, profiling_attempts) 
    VALUES 
    (:id, :name, :rate, :hub_name, :opening_balance, :account_number, :account_name, :bank_name, :freeze_flag, :created_at, :updated_at, :role, :recipient_id, :collection_center_id, :status_message, :profiling_attempts)
    ON CONFLICT (id) DO NOTHING";

    $stmt = $conn_finance->prepare($sql);

    $current_date_time = date('Y-m-d H:i:s');
    $rate = 0;
    $hub_name = '';
    $opening_balance = 0;
    $freeze_flag = 0;
    $recipient_id = '';
    $collection_center_id = '';
    $status_message = '';
    $profiling_attempts = 0;

    foreach ($processed_results as $data) {
        $stmt->bindParam(':id', $data['id']);
        $stmt->bindParam(':name', $data['name']);
        $stmt->bindParam(':rate', $rate);
        $stmt->bindParam(':hub_name', $hub_name);
        $stmt->bindParam(':opening_balance', $opening_balance);
        $stmt->bindParam(':account_number', $data['account_number']);
        $stmt->bindParam(':account_name', $data['account_name']);
        $stmt->bindParam(':bank_name', $data['bank_name']);
        $stmt->bindParam(':freeze_flag', $freeze_flag);
        $stmt->bindParam(':created_at', $current_date_time);
        $stmt->bindParam(':updated_at', $current_date_time);
        $stmt->bindParam(':role', $data['role']);
        $stmt->bindParam(':recipient_id', $recipient_id);
        $stmt->bindParam(':collection_center_id', $collection_center_id);
        $stmt->bindParam(':status_message', $status_message);
        $stmt->bindParam(':profiling_attempts', $profiling_attempts);

        $stmt->execute();
    }
}

function get_float_ids($conn_finance)
{
    try {
        $query = "SELECT id FROM float_payment_config";
        $stmt = $conn_finance->prepare($query);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_COLUMN);

        return $results;
    } catch (Exception $e) {
        error_log("Error fetching data" . $e->getMessage());
        return [];
    }
}

function fetch_independent_transporter($conn_finance, $conn_transport) 
{
    try {
        $ids = get_float_ids($conn_finance);
        $placeholder_string = join(",", array_fill(0,count($ids), "?"));
        $query = "SELECT transporter_id AS id, CONCAT(first_name, ' ', last_name) AS name, bank_name, account_number, account_name, 'Independent Transporter' AS role 
        FROM third_party_transporter WHERE transporter_id NOT IN ({$placeholder_string})";

        $stmt = $conn_transport->prepare($query);
        $stmt->execute(array_values($ids));
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $processed_results = $results;

        insert_into_float_payment_config($conn_finance, $processed_results);

    } catch (Exception $e) {
        error_log("Unable to complete process" . $e->getMessage());
        return [];
    }
}

function fetch_company_transporter($conn_finance, $conn_transport)
{
    try {
        $ids = get_float_ids($conn_finance);
        $placeholder_string = join(",", array_fill(0,count($ids), "?"));
        $query = "SELECT 
        CASE WHEN staff_id ILIKE 'IK%' AND LENGTH(staff_id) = 10 THEN CONCAT(staff_id, '_0000')
        ELSE staff_id 
        END AS id, CONCAT(first_name, ' ', last_name) AS name, bank_name, account_number, account_name, 'Company Transporter' AS role 
        FROM company_transporter WHERE staff_role NOT IN ('CFO', 'OFO') AND staff_id NOT IN ({$placeholder_string})";

        $stmt = $conn_transport->prepare($query);
        $stmt->execute(array_values($ids));
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $processed_results = $results;

        insert_into_float_payment_config($conn_finance, $processed_results);

    } catch (Exception $e) {
        error_log("Unable to complete process" . $e->getMessage());
        return [];
    }
}

function initiate_processing($conn_finance, $conn_transport, $conn_inventory)
{
    try {
        $start = microtime(true);
        echo  date('Y-m-d H:i:s.u') . "  🎶🎶🎶  =====TRANSPORTER FETCH CRON ACTIVATED, READY FOR TAKEOFF==== 🎶🎶🎶 <br /> \n";
        $logArray = [];

        echo  date('Y-m-d H:i:s.u') . " ✋✋......Now fetching Third Party Transporter and inserting into float payment config table... ✋✋ <br /> \n";
        fetch_independent_transporter($conn_finance, $conn_transport);
        echo  date('Y-m-d H:i:s.u') . " ✔✔✔✔ ......Done inserting records into float payment config table...✔✔✔✔ <br /> \n";

        echo  date('Y-m-d H:i:s.u') . " ✋✋......Now fetching Company Transporter and inserting into float payment config table... ✋✋ <br /> \n";
        fetch_company_transporter($conn_finance, $conn_transport);
        echo  date('Y-m-d H:i:s.u') . " ✔✔✔✔ ......Done inserting records into float payment config table...✔✔✔✔ <br /> \n";

        echo  date('Y-m-d H:i:s.u') . " ✋✋......Now updating transporter id records in transport controller table... ✋✋ <br /> \n";
        $updateTransporterId = update_transporter_id($conn_inventory);
        echo  date('Y-m-d H:i:s.u') . " ✔✔✔✔ ......Done updating transporter id records in transport controller table...✔✔✔✔ <br /> \n";

        $end = microtime(true);
        $elapsed = $end - $start;
        $logArray[] = getLogString("FLOAT CRON",  "🎵🎵🎵 Bravo!!!! CRON ran successfully in " . $elapsed . " seconds 🎵🎵🎵");
        $logArray[] = getLogString("FLOAT CRON", '✍✍ - ' .  $updateTransporterId . " - Transporter ID(s) Updated===");
        $logArray[] = getLogString("FLOAT CRON", '🔊🔊🔊🔊🔊 - JOB WELL DONE - 🔊🔊🔊🔊🔊');
        $logArray[] = getLogString("FLOAT CRON",  "👏👏👏 FLOAT CRON TOUCH DOWN SUCCESSFULLY...👏👏👏👏");

        foreach ($logArray as $log) {
            echo $log;
        }
    } catch (Exception $e) {
        error_log('Initiate Processing Error: ' . $e->getMessage());
        echo " 🙈🙈🙈...Something went wrong. Float cron failed: 🙈🙈🙈 " . $e->getMessage();
    }
}

initiate_processing($conn_finance, $conn_transport, $conn_inventory);

?>