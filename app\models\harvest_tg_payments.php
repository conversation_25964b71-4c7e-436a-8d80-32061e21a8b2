<?php
require_once(__DIR__ . "/../controllers/__calculations.php");
require_once(__DIR__ . "/../controllers/__data_helpers.php");

class TGPayments{
        /* selectMany : Runs a select query on harvest_trust_group_payments only selecting pending TGs 
         * @param $con : database connection object
         * @param $ik_arr : an array of ik_numbers to be used in the IN block
         * returns - null
         */
    public static function selectMany($con, $ik_arr){
        try{
            if(empty($ik_arr)) return [];
            $placeholder_string = join(",", array_fill(0,count($ik_arr), "?"));
            $query = "SELECT * FROM harvest_trust_group_payments WHERE updated_flag = 0 OR ik_number IN ({$placeholder_string})";
            $stmt = $con->prepare($query);
            $stmt->execute(array_values($ik_arr));
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            }
        
        catch(Exception $err){
            throw new Exception("Ooops!!! Looks like we couldn't fetch all TG Payment records - ". $err->getMessage());
        }
    }

    public static function selectForPayment($con){
        try{
            $query = "
            SELECT ik_number, contractual_flag, member_failed_expectation, finance_dnp ,shp_dnp, tech_dnp, finance_dnp_override, shp_dnp_override, tech_dnp_override, max_payout_flag, all_flags_override FROM harvest_trust_group_payments WHERE net_harvest_advance > 0";
            $stmt = $con->prepare($query);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            }
        catch(Exception $err){
            throw new Exception("Ooops!!! Looks like we couldn't fetch all TG Payment records - ". $err->getMessage());
        }
    }
    
    public static function selectAll($con){
        try{
            $query = "
            SELECT ik_number, id_loan_size, no_of_bags_marketed,total_grain_value, misc_account, net_harvest_advance, recipient_id, 
            COALESCE(c.amount,0) AS amount_paid, payment_ready,payment_risk,
			CASE
            WHEN net_harvest_advance - COALESCE(c.amount,0) < 0 THEN 0
            ELSE net_harvest_advance - COALESCE(c.amount,0)
            END AS amount_to_be_paid,
			CASE
            WHEN net_harvest_advance = 0 AND COALESCE(c.amount,0) = 0 THEN -2
            WHEN COALESCE(c.amount,0) = net_harvest_advance THEN 1
			WHEN COALESCE(c.amount,0) = 0 THEN 0
			WHEN COALESCE(c.amount,0) > net_harvest_advance THEN 2
			WHEN COALESCE(c.amount,0) < net_harvest_advance THEN -1
			ELSE 1
			END
			AS payment_status
			FROM harvest_trust_group_payments a LEFT OUTER JOIN member_cards b 
            ON a.ik_number = b.id LEFT OUTER JOIN (SELECT payee_id, SUM(amount) AS amount FROM harvest_transactions 
            WHERE payment_type = 'harvest_advance' GROUP BY payee_id UNION SELECT payee_id, SUM(amount) FROM payment_queue 
            WHERE payment_category = 'harvest_advance' AND attempts < 1 GROUP BY payee_id) c ON a.ik_number = c.payee_id";

            $stmt = $con->prepare($query);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            }
        catch(Exception $err){
            throw new Exception("Ooops!!! Looks like we couldn't fetch all TG Payment records - ". $err->getMessage());
        }
    }

    public static function selectPaymentReady($con, $id = null){
        try{
            // $query = "
            // SELECT ik_number, id_loan_size, no_of_bags_marketed,total_grain_value::integer, net_harvest_advance::integer,COALESCE(amount_paid,0) AS amount_paid, payment_risk,payment_ready,
            // CASE 
            // WHEN net_harvest_advance = 0 THEN -2
            // WHEN net_harvest_advance - COALESCE(amount_paid,0) = net_harvest_advance THEN 0
            // ELSE -1
            // END AS payment_status,
            // net_harvest_advance::integer - COALESCE(amount_paid,0)AS amount_to_be_paid,recipient_id, account_number, bank_name FROM harvest_trust_group_payments
            // LEFT OUTER JOIN (SELECT id,recipient_id,account_number,bank_name FROM member_cards  WHERE category = 'trust_group') cards ON ik_number = id LEFT OUTER JOIN 
            // (SELECT payee_id,SUM(amount) AS amount_paid FROM harvest_transactions GROUP BY payee_id,payment_type HAVING payment_type = 'harvest_advance') trans
            // ON  ik_number = payee_id
	        // WHERE (net_harvest_advance::integer - COALESCE(amount_paid,0) > 0  )                                                                                                                           
	        // AND NOT ik_number IN (SELECT DISTINCT payee_id FROM payment_queue
            // WHERE payment_category = 'harvest_advance')";

            // $stmt = $con->prepare($query);
            // $stmt->execute();
            // return $stmt->fetchAll(PDO::FETCH_ASSOC);
            $query = "SELECT t.ik_number, t.id_loan_size, t.no_of_bags_marketed, t.total_grain_value::integer, t.net_harvest_advance::integer, COALESCE(h.amount_paid, 0) AS amount_paid, t.payment_ready,
            CASE
                WHEN t.no_of_bags_marketed < 20 * t.id_loan_size THEN 0
                WHEN t.no_of_bags_marketed = 20 * t.id_loan_size THEN 1
                ELSE 2
            END AS status,
            -- CASE
            --     WHEN t.shp_dnp_override = 1 THEN 1
            --     WHEN (t.finance_dnp = 1 AND t.shp_dnp = 1) OR t.finance_dnp = 1 THEN 2
            --     WHEN t.shp_dnp = 1 THEN 3
            --     ELSE 0
            -- END AS payment_risk,
            CASE
                WHEN (t.finance_dnp = 1 AND t.finance_dnp_override = 1) 
                OR (t.shp_dnp = 1 AND t.shp_dnp_override = 1) 
                OR (t.tech_dnp = 1 AND t.tech_dnp_override = 1) THEN 1
                WHEN t.finance_dnp = 1 AND t.finance_dnp_override = 0 THEN 2
                WHEN t.shp_dnp = 1 AND t.shp_dnp_override = 0 THEN 3
                WHEN t.tech_dnp = 1 AND t.tech_dnp_override = 0 THEN 4
                ELSE 0
            END AS payment_risk,
            CASE
                WHEN t.net_harvest_advance = COALESCE(h.amount_paid, 0) THEN 1
                WHEN COALESCE(h.amount_paid, 0) > t.net_harvest_advance AND t.net_harvest_advance > 0 THEN 2
                WHEN t.net_harvest_advance > COALESCE(h.amount_paid, 0) AND COALESCE(h.amount_paid) > 0 THEN -1
                WHEN COALESCE(h.amount_paid, 0) = 0 AND t.net_harvest_advance > 0 THEN 0
                ELSE -2
            END AS payment_status,
            t.net_harvest_advance::integer - COALESCE(h.amount_paid, 0) AS amount_payable, c.recipient_id, c.account_number, c.account_name, c.bank_name, t.loan_before_harvest, t.misc_account, t.total_cost
            FROM harvest_trust_group_payments t
            LEFT JOIN (SELECT id, recipient_id, account_number, account_name, bank_name FROM member_cards WHERE category = 'trust_group') c ON t.ik_number = c.id
            LEFT JOIN (SELECT payee_id, SUM(amount) as amount_paid FROM harvest_transactions WHERE payment_type = 'harvest_advance' GROUP BY payee_id) h ON t.ik_number = h.payee_id 
            WHERE (t.net_harvest_advance::integer > 0) 
            AND t.ik_number NOT IN (SELECT DISTINCT payee_id FROM payment_queue WHERE payment_category = 'harvest_advance') AND (t.payment_ready + t.all_flags_override::integer) > 0";

            if ($id) {
                $cleanIds = preg_replace("/['\"]/", "", $id);
                $idArray = array_map('trim', explode(',', $cleanIds));
                $placeholders = implode(',', array_fill(0, count($idArray), '?'));
                $query .= " AND t.ik_number IN ($placeholders)";
            }

            $stmt = $con->prepare($query);
            // $stmt->execute();
            if ($id) {
                $stmt->execute($idArray);
            } else {
                $stmt->execute();
            }
            $tg_members = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $ik_num = [];
            foreach ($tg_members as $key) {
                if (isset($key['ik_number'])) {
                    $ik_num[] = $key['ik_number'];
                }
            }

            $tg_leaders = getTGLeaders($ik_num);

            $indexed_members = [];
            foreach ($tg_members as $member) {
                $indexed_members[$member['ik_number']] = $member;
            }

            // Merge the data from arrayA and arrayB
            $result = [];
            foreach ($tg_leaders as $leader) {
                $ikN = $leader['ik_number'];
                if (isset($indexed_members[$ikN])) {
                    $mergedItem = array_merge($leader, $indexed_members[$ikN]);
                $result[] = $mergedItem;
                }
            }

            return $result;
        }
        catch(Exception $err){
            throw new Exception("Ooops!!! Looks like we couldn't fetch all TG Member Payment records - ". $err->getMessage());
        }
    }

    public static function selectFromFile($con,$tg_arr){
        try{
            // $placeholder_string = join(',', array_fill(0,count($tg_arr),'?'));

            // $query = "
            // SELECT ik_number, id_loan_size, no_of_bags_marketed,total_grain_value::integer, net_harvest_advance::integer,COALESCE(amount_paid,0) AS amount_paid, payment_risk,payment_ready,
            // CASE 
            // WHEN net_harvest_advance = 0 THEN -2
            // WHEN net_harvest_advance - COALESCE(amount_paid,0) = net_harvest_advance THEN 0
            // ELSE -1
            // END AS payment_status,
            // net_harvest_advance::integer - COALESCE(amount_paid,0)AS amount_to_be_paid,recipient_id, account_number, bank_name FROM harvest_trust_group_payments
            // LEFT OUTER JOIN (SELECT id,recipient_id,account_number,bank_name FROM member_cards  WHERE category = 'trust_group') cards ON ik_number = id LEFT OUTER JOIN 
            // (SELECT payee_id,SUM(amount) AS amount_paid FROM harvest_transactions GROUP BY payee_id,payment_type HAVING payment_type = 'harvest_advance') trans
            // ON  ik_number = payee_id
	        // WHERE (net_harvest_advance::integer - COALESCE(amount_paid,0) > 0  )                                                                                                                           
	        // AND NOT ik_number IN (SELECT DISTINCT payee_id FROM payment_queue
            // WHERE payment_category = 'harvest_advance') AND ik_number IN ($placeholder_string)";

            // $stmt = $con->prepare($query);
            // $stmt->execute($tg_arr);
            // return $stmt->fetchAll(PDO::FETCH_ASSOC);

            $placeholder_string = join(',', array_fill(0,count($tg_arr),'?'));

            $query = "SELECT t.ik_number, t.id_loan_size, t.no_of_bags_marketed, t.total_grain_value::integer, t.net_harvest_advance::integer, COALESCE(h.amount_paid, 0) AS amount_paid, t.payment_ready,
            CASE
                WHEN t.no_of_bags_marketed < 20 * t.id_loan_size THEN 0
                WHEN t.no_of_bags_marketed = 20 * t.id_loan_size THEN 1
                ELSE 2
            END AS status,
            CASE
                WHEN t.shp_dnp_override = 1 THEN 1
                WHEN (t.finance_dnp = 1 AND t.shp_dnp = 1) OR t.finance_dnp = 1 THEN 2
                WHEN t.shp_dnp = 1 THEN 3
                ELSE 0
            END AS payment_risk,
            CASE
                WHEN t.net_harvest_advance = COALESCE(h.amount_paid, 0) THEN 1
                WHEN COALESCE(h.amount_paid, 0) > t.net_harvest_advance AND t.net_harvest_advance > 0 THEN 2
                WHEN t.net_harvest_advance > COALESCE(h.amount_paid, 0) AND COALESCE(h.amount_paid) > 0 THEN -1
                WHEN COALESCE(h.amount_paid, 0) = 0 AND t.net_harvest_advance > 0 THEN 0
                ELSE -2
            END AS payment_status,
            t.net_harvest_advance::integer - COALESCE(h.amount_paid, 0) AS amount_payable, c.recipient_id, c.account_number, c.account_name, c.bank_name, t.loan_before_harvest, t.misc_account, t.total_cost
            FROM harvest_trust_group_payments t
            LEFT JOIN (SELECT id, recipient_id, account_number, account_name, bank_name FROM member_cards WHERE category = 'trust_group') c ON t.ik_number = c.id
            LEFT JOIN (SELECT payee_id, SUM(amount) as amount_paid FROM harvest_transactions WHERE payment_type = 'harvest_advance' GROUP BY payee_id) h ON t.ik_number = h.payee_id 
            WHERE (t.net_harvest_advance::integer > 0)
            AND t.ik_number NOT IN (SELECT DISTINCT payee_id FROM payment_queue WHERE payment_category = 'harvest_advance') AND t.ik_number IN ({$placeholder_string}) AND (t.payment_ready + t.all_flags_override) > 0";

            $stmt = $con->prepare($query);
            $stmt->execute($tg_arr);
            $tg_members = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $ik_num = [];
            foreach ($tg_members as $key) {
                if (isset($key['ik_number'])) {
                    $ik_num[] = $key['ik_number'];
                }
            }

            $tg_leaders = getTGLeaders($ik_num);

            $indexed_members = [];
            foreach ($tg_members as $member) {
                $indexed_members[$member['ik_number']] = $member;
            }

            // Merge the data from arrayA and arrayB
            $result = [];
            foreach ($tg_leaders as $leader) {
                $ikN = $leader['ik_number'];
                if (isset($indexed_members[$ikN])) {
                    $mergedItem = array_merge($leader, $indexed_members[$ikN]);
                $result[] = $mergedItem;
                }
            }

            return $result;
        }
        catch(Exception $err){
            throw new Exception("Ooops!!! Looks like we couldn't fetch all TG Member Payment records - ". $err->getMessage());
        }
    }

    public static function selectNoRiskGroups($con){
        try{
            $query = "
            SELECT ik_number AS payee_id, net_harvest_advance AS amount, recipient_id, account_number, bank_name FROM harvest_trust_group_payments a JOIN member_cards b
             ON a.ik_number = b.id  WHERE recipient_id ILIKE '%RCP%' AND NOT a.ik_number IN 
             (SELECT  payee_id FROM payment_queue UNION SELECT payee_id FROM harvest_transactions WHERE payment_type = 'harvest_advance')";
            $stmt = $con->prepare($query);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            }
        catch(Exception $err){
            throw new Exception("Ooops!!! Looks like we couldn't fetch all TG Payment records - ". $err->getMessage());
        }
    }

    public static function updateOne($con, $ik_number, $update_pairs)
    {
        $update_string = '';
        try {
            // build up the update string
            foreach ($update_pairs as $key => $value) {
                $update_string .= "{$key} ='{$value}',";
            }
            $stmt = $con->prepare("UPDATE harvest_trust_group_payments SET {$update_string} updated_at = NOW() WHERE ik_number = ?");
            $stmt->execute([$ik_number]);
        } catch (Exception $err) {
            throw new Exception("Ooops!!! Looks like we couldn't update all checker flags - " . $err->getMessage());
        }
    }
        /* updateMany : updates columns for many records on harvest_trust_group_payments using IN keyword
         * @param $con : database connection object
         * @param $ik_num_array : an array of HSF records
         * @param $update_pairs : an assoc array of column names and the values they should be updated to
         * returns - null
         */
    public static function updateMany($con, $ik_num_array, $update_pairs ){
        $ik_numbers = $ik_num_array;
        $update_string = '';
        try {
            // build up the update string
            foreach ($update_pairs as $key => $value) {
                $update_string .= "{$key} ='{$value}',";
            }
            $chunks = array_chunk($ik_numbers, 65000);
            foreach ($chunks as $eachChunk) {
                $placeholder_string = join(",", array_fill(0, count($eachChunk), "?"));
                $query = "UPDATE harvest_trust_group_payments SET {$update_string} updated_at = NOW() WHERE ik_number IN ({$placeholder_string})";
                $stmt = $con->prepare($query);
                $stmt->execute(array_values($eachChunk));
            }
        }
         catch (Exception $err) {
            throw new Exception("Ooops!!! Looks like we couldn't update all TG Payment Records - " . $err->getMessage());
        }
       }

        /* insertMany : inserts records into harvest_trust_group_payments table using prepared statements and parameters
         * @param $con : database connection object
         * @param $insertArray : an array of HSF records
         * @param $length : specifies how many records to be inserted at once
         * returns - null
         */
    public static function insertMany($con, $insertArray, $length){
        $tg_payments_cols_arr = [
            'ik_number','product','season','id_package','id_loan_size','total_grain_value','loan_before_harvest','total_cost','misc_account',
            'no_of_bags_marketed','shp_dnp','shp_dnp_override','finance_dnp','contractual_flag','financial_default','max_payout_flag','max_payout_size_flag','override_max_payout',
            'member_failed_expectation','override_failed_expectation','total_harvest_advance','net_harvest_advance','payment_ready',
            'updated_flag','created_at','updated_at','all_flags_override'
           ];
        
        $duplicate_string = generateDuplicateStringPG($tg_payments_cols_arr, ['ik_number','product','season']);

        $tg_payments_cols_str = join(",", $tg_payments_cols_arr);

        try {
            $chunks = array_chunk($insertArray, $length);
            foreach ($chunks as $eachChunk) {
                $placeholder_array = [];
                for($i=0; $i < count($eachChunk); $i++){
                    $placeholder_array[] = "(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
                }
                $placeholder_string = join(",", $placeholder_array);
                $query = "INSERT INTO harvest_trust_group_payments ({$tg_payments_cols_str}) VALUES {$placeholder_string} ON CONFLICT(ik_number,product,season) DO UPDATE SET {$duplicate_string}";
                $stmt = $con->prepare($query);
                $oneMultiInsertArray = [];
                foreach ($eachChunk as $eachRecord) {
                    $oneMultiInsertArray[] = $eachRecord["ik_number"]; //  ik_number
                    $oneMultiInsertArray[] = $eachRecord["product"]; // product
                    $oneMultiInsertArray[] = $eachRecord["season"]; // season
                    $oneMultiInsertArray[] = $eachRecord["id_package"];// id_package
                    $oneMultiInsertArray[] = $eachRecord["id_loan_size"] ?? 'loan'; // id_loan_size
                    $oneMultiInsertArray[] = $eachRecord["total_grain_value"] ?? 0; // id_loan_size
                    $oneMultiInsertArray[] = $eachRecord["loan_before_harvest"] ?? 0; // loan_before_harvest
                    $oneMultiInsertArray[] = $eachRecord["total_cost"] ?? 0;// total_cost
                    $oneMultiInsertArray[] = $eachRecord["misc_account"] ?? 0; // misc_account
                    $oneMultiInsertArray[] = $eachRecord["no_of_bags_marketed"] ?? 0; // no_of_bags_marketed
                    $oneMultiInsertArray[] = $eachRecord["shp_dnp"] ?? 0; // shp_dnp
                    $oneMultiInsertArray[] = $eachRecord["shp_dnp_override"] ?? 0; //shp_dnp_override
                    $oneMultiInsertArray[] = $eachRecord["finance_dnp"] ?? 0; // finance_dnp
                    $oneMultiInsertArray[] = $eachRecord["contractual_flag"] ?? 0; // contractual_flag
                    $oneMultiInsertArray[] = $eachRecord["financial_default"] ?? 0; // financial_default
                    $oneMultiInsertArray[] = $eachRecord["max_payout_flag"] ?? 0;// max_payout_flag
                    $oneMultiInsertArray[] = $eachRecord["max_payout_size_flag"] ?? 0; // max_payout_size_flag
                    $oneMultiInsertArray[] = $eachRecord["override_max_payout"] ?? 0; // override_max_payout
                    $oneMultiInsertArray[] = $eachRecord["member_failed_expectation"] ?? 0; //member_exceeded_expectation;
                    $oneMultiInsertArray[] = $eachRecord["override_failed_expectation"] ?? 0; // override_failed_expectation
                    $oneMultiInsertArray[] = $eachRecord["total_harvest_advance"] ?? 0; // total_harvest_advance
                    $oneMultiInsertArray[] = $eachRecord["net_harvest_advance"] ?? 0; // net_harvest_advance
                    $oneMultiInsertArray[] = $eachRecord["payment_ready"] ?? 0; // payment_ready;
                    $oneMultiInsertArray[] = $eachRecord["updated_flag"] ?? 0; // updated_flag;
                    $oneMultiInsertArray[] = $eachRecord["created_at"] ?? date('Y-m-d H:i:s'); // created_at;
                    $oneMultiInsertArray[] = date('Y-m-d H:i:s'); // updated_at
                    $oneMultiInsertArray[] = $eachRecord["all_flags_override"]; // updated_at
                }
                $stmt->execute($oneMultiInsertArray);
            }
        } catch (Exception $err) {
            throw new Exception("Ooops!!! Looks like we couldn't insert all harvest TG records - " . $err->getMessage());
        }
    }
    public static function selectCustom($con, $cols_arr, $where_arr){
             
        try {
           //$cols = join(",", $cols_arr);
           //$query = "SELECT {$cols} FROM harvest_trust_group_payments ";
           $query = "SELECT t.ik_number, t.id_loan_size, t.net_harvest_advance, t.net_harvest_advance::integer - COALESCE(h.amount_paid, 0) AS amount_payable, c.account_number, t.max_payout_flag
           FROM harvest_trust_group_payments t
           LEFT JOIN (SELECT id, account_number, recipient_id FROM member_cards WHERE category = 'trust_group') c ON t.ik_number = c.id
           LEFT JOIN (SELECT payee_id, SUM(amount) AS amount_paid FROM harvest_transactions WHERE payment_type = 'harvest_advance' GROUP BY payee_id) h ON t.ik_number = h.payee_id
           WHERE c.account_number NOT IN (SELECT DISTINCT account_number FROM payment_queue WHERE payment_category = 'harvest_advance')
           AND c.recipient_id NOT IN (SELECT DISTINCT recipient_id FROM harvest_transactions WHERE payment_type = 'harvest_advance' and payee_id != :ik_number) ";
           $where_clause = '';

             if(gettype($where_arr) == 'array'){
               $index = 0;
               // build up where clause
               foreach ($where_arr as $key => $value) {
                   $where_clause .= "{$key} ='{$value}'";
                   if($index < count($where_arr) - 1 )
                   $where_clause .= " AND";
                   ++$index;
               }  
            }

               if(!empty($where_clause)) $query .= "AND {$where_clause}";
               $stmt = $con->prepare($query);
               $stmt->bindValue(':ik_number', $where_arr['t.ik_number'], PDO::PARAM_STR);
               $stmt->execute();
               $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
               if(count($result) < 1) return false;
               return $result;
           }
       catch(Exception $err){
           throw new Exception("Ooops!!! Looks like we couldn't select all completed transactions - ". $err->getMessage());
       }
   }
}

?>