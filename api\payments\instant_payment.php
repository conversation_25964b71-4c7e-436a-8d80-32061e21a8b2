<?php
require_once(__DIR__ . '/../../constants.php');
require_once(__DIR__ . '/../../connect_db.php');
require_once(__DIR__ . '/../../app/controllers/__payment_validations.php');
require_once(__DIR__ . '/../auth.php');
require_once(__DIR__ . '/../../app/models/exception.php');
require_once(__DIR__ . '/../../app/models/payment_queue.php');
require_once(__DIR__ . '/../../app/controllers/cors.php');
require_once(__DIR__ . '/../../app/models/app_logs.php');


cors();
(function () {
    try {
        if ($_SERVER['REQUEST_METHOD'] != 'POST') throw new CustomException(403, "Request Method Not Allowed");
        $user = validateUser();
        
        $driver = new DBDriver;
        $conn = $driver->connectPgSql(PG_FINANCE_DB_NAME);
        $json = file_get_contents('php://input');
        $data = json_decode($json, true);

        $initiator_name = $user->data->staff_name;
        $initiator_role = $user->data->config[0]->permission_name;

        if (!isValidInstantPaymentPayload($data)) {
            throw new CustomException(400, "Invalid payment payload, one or more parameters are missing or invalid");
        }

        PaymentQueue::insertInstantPayment($conn, $data, $initiator_name, $initiator_role);
        http_response_code(201);

        $payment_response = setResponse(true, "Payment has been successfully queued", [], null);
        echo json_encode($payment_response);

        AppLogs::insertOne($conn, generateLogs($user->data->staff_id, "SUBMITTED SINGLE INSTANT PAYMENT", $data));
    } catch (CustomException $e) {
        http_response_code($e->status ?? 500);
        echo json_encode(setResponse(false, "Something went wrong... Payment not queued ", [], $e->getMessage()));
    }
})();


?>