<?php
require_once '../../connect_db.php';
require_once '../../constants.php';
require_once '../../app/controllers/cors.php';
require_once '../../app/models/exception.php';
require_once '../../app/models/fetch_dnp.php';
require_once('../auth.php');
require_once("../../app/controllers/__data_helpers.php");

cors();

if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    
    $user = validateUser();
    $json = file_get_contents('php://input');
    $data = json_decode($json, true);

    $id = isset($_GET['id']) ? $_GET['id'] : null;
    try {
        $driver = new DBDriver;
        $conn_finance = $driver->connectPgSql(PG_FINANCE_DB_NAME);
        $conn_portfolio = $driver->connectPgSql(PG_PORTFOLIO_DB_NAME);
        $conn_planning = $driver->connectPgSql(PG_PLANNING_DB_NAME);
        $conn_bg_signon = $driver->connectPgSql(PG_BG_SIGNON_DB_NAME);

        $result = get_DNP($conn_finance, $conn_portfolio, $conn_planning, $conn_bg_signon, $id);

        if (count($result) == 1) {
            $result = $result[0];
        }

        if ($result) {
            $code = 200;
            $response = get_response(true, "Data fetched successfully", $result, null);
        } else {
            $code = 404;
            $response = get_response(false, "No data found", null, "No records found.");
        }
    } catch (PDOException $e) {
        $response = get_response(false, "An SQL error occurred", null, $e->getMessage());  
        $code = 500;
    } catch (CustomException $exception) {
        $response = get_response(false, "An error occurred while fetching the data", null, $exception->getMessage());
        $code = 400;
    }
} else {
    $response = get_response(false, "Request Method Not Allowed", null, "Only GET method is allowed.");
    $code = 403;
}

http_response_code($code);
echo json_encode($response);

function get_response($status, $message, $data = null, $error = null)
{
    return [
        'success' => $status,
        'message' => $message,
        'data' => $data,
        'error' => $error
    ];
}

