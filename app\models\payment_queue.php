<?php
require_once(__DIR__ . "/../controllers/__calculations.php");
require_once(__DIR__ . "/../controllers/__data_helpers.php");
require_once(__DIR__ . '/../../app/models/payment_tracker.php');
require_once(__DIR__ . '/../../app/controllers/__payment_validations.php');
require_once(__DIR__ . '/../../app/models/exception.php');
require_once(__DIR__ . '/../../api/payment_helper.php');

class PaymentQueue
{
    /* selectMany : Runs a select query on payment_queue only selecting pending payments 
         * @param $con : database connection object
         * @param $ik_arr : an array of ids to be used in the IN block
         * returns - null
         * 
         * Makes changes to this to include where is_approved is 1 
         */

    //Make this query generic
    public static function selectPending($con)
    {
        try {
            $query = "SELECT id, attempts, payload FROM payment_queue WHERE attempts = 0 AND approval_flag = 1 ORDER BY created_at LIMIT 100";
            $stmt = $con->prepare($query);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $err) {
            throw new Exception("Ooops!!! Looks like we couldn't fetch all queued payments - " . $err->getMessage());
        }
    }

    public static function selectAttempted($con)
    {
        try {
            $query = "SELECT id, reference, payload FROM payment_queue WHERE attempts > 0 AND approval_flag = 1 AND extra_info = null ORDER BY created_at";
            $stmt = $con->prepare($query);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $err) {
            throw new Exception("Ooops!!! Looks like we couldn't fetch all queued payments - " . $err->getMessage());
        }
    }

    public static function updateOne($con, $id, $update_pairs)
    {
        $update_string = '';
        try {
            // build up the update string
            $index = 0;
            foreach ($update_pairs as $key => $value) {
                $update_string .= "{$key} ='{$value}'";
                if ($index < count($update_pairs) - 1)
                    $update_string .= ",";
                ++$index;
            }
            $stmt = $con->prepare("UPDATE payment_queue SET {$update_string} WHERE id = ?");
            $stmt->execute([$id]);
        } catch (Exception $err) {
            throw new Exception("Ooops!!! Looks like we couldn't update all queued payments - " . $err->getMessage());
        }
    }
    /* updateMany : updates columns for many records on payment_queue using IN keyword
         * @param $con : database connection object
         * @param $id_arr : an array of HSF records
         * @param $update_pairs : an assoc array of column names and the values they should be updated to
         * returns - null
         */
    public static function updateMany($con, $id_arr, $update_pairs)
    {
        $ids = $id_arr;
        $update_string = '';
        try {
            // build up the update string
            foreach ($update_pairs as $key => $value) {
                $update_string .= "{$key} ='{$value}',";
            }
            $chunks = array_chunk($ids, 65000);
            foreach ($chunks as $eachChunk) {
                $placeholder_string = join(",", array_fill(0, count($eachChunk), "?"));
                $query = "UPDATE payment_queue SET {$update_string} updated_at = NOW() WHERE id IN ({$placeholder_string})";
                $stmt = $con->prepare($query);
                $stmt->execute(array_values($eachChunk));
            }
        } catch (Exception $err) {
            throw new Exception("Ooops!!! Looks like we couldn't update all queued payments - " . $err->getMessage());
        }
    }

    /* insertMany : inserts records into payment_queue table using prepared statements and parameters
         * @param $con : database connection object
         * @param $insertArray : an array of HSF records
         * @param $length : specifies how many records to be inserted at once
         * returns - null
         */
    // public static function insertMany($con, $insertArray, $length){
    //     $tg_payments_cols_arr = ['reference', 'payee_id', 'payment_category','amount','payee_recipient_id',
    //                              'account_number','bank_name','payload'
    //                             ];
    //     $payment_queue_cols_arr = join(",", $tg_payments_cols_arr);
    //     try {
    //         $chunks = array_chunk($insertArray, $length);
    //         foreach ($chunks as $eachChunk) {
    //             $placeholder_array = [];
    //             for($i=0; $i < count($eachChunk); $i++){
    //                 $placeholder_array[] = "(?,?,?,?,?,?,?,?)";
    //             }
    //             $placeholder_string = join(",", $placeholder_array);
    //             $query = "INSERT INTO payment_queue ({$payment_queue_cols_arr}) VALUES {$placeholder_string} ON CONFLICT(id) DO UPDATE SET {$duplicate_string}";
    //             $stmt = $con->prepare($query);
    //             $oneMultiInsertArray = [];
    //             foreach ($eachChunk as $eachRecord) {
    //                 $oneMultiInsertArray[] = $eachRecord["action"]; //  action
    //                 $oneMultiInsertArray[] = json_encode($eachRecord["payload"]); // payload
    //                 $oneMultiInsertArray[] = $eachRecord["attempts"]; // attempts
    //             }
    //             $stmt->execute($oneMultiInsertArray);
    //         }
    //     } catch (Exception $err) {
    //         throw new Exception("Ooops!!! Looks like we couldn't insert all harvest TG records - " . $err->getMessage());
    //     }
    // }

    public static function insertOne($con, $arr)
    {
        $payment_queue_cols_arr = [
            'reference', 'payee_id', 'payment_category', 'amount', 'payee_recipient_id',
            'account_number', 'bank_name', 'payload'
        ];
        $insert_cols = join(",", $payment_queue_cols_arr);
        try {
            // Identify the table to fetch bank details from based on payment type
            $accountdetailsTable = ($arr['payment_type'] === 'float_payment') ? 'float_payment_config' : 'member_cards';

            $initialStmt = $con->prepare("SELECT bank_name, account_number FROM {$accountdetailsTable} WHERE id = ? AND recipient_id = ?");
            $initialStmt->execute([$arr['payee_id'], $arr['recipient_id']]);
            $bank_details = $initialStmt->fetchAll(PDO::FETCH_ASSOC);
            if (count($bank_details) < 1) throw new Exception("Either recipient_id is invalid or payee has not been profiled on our system");

            // $duplicateStmt = $con->prepare("SELECT action FROM payment_queue WHERE payee_id =? AND amount = ? AND payment_category =? AND created_at > NOW() - INTERVAL '2 hours'");
            $duplicateStmt = $con->prepare("SELECT action FROM payment_queue WHERE payee_id =? AND amount = ? AND payment_category =?");
            $duplicateStmt->execute([$arr['payee_id'], $arr['amount'], $arr['payment_type']]);
            $duplicateTransaction = $duplicateStmt->fetchAll(PDO::FETCH_ASSOC);
            if (count($duplicateTransaction) > 0) throw new Exception("Possible duplicate transaction detected. Please confirm the authenticity of this transaction");

            $duplicateStmt2 = $con->prepare("SELECT payee_id FROM harvest_transactions WHERE payee_id =? AND amount = ? AND payment_type =?");
            $duplicateStmt2->execute([$arr['payee_id'], $arr['amount'], $arr['payment_type']]);
            $duplicateTransaction2 = $duplicateStmt2->fetchAll(PDO::FETCH_ASSOC);
            if (count($duplicateTransaction2) > 0) throw new Exception("Possible duplicate transaction detected. Please confirm the authenticity of this transaction");



            $arr['bank_name'] = $bank_details[0]['bank_name'];
            $arr['account_number'] = $bank_details[0]['account_number'];
            $query = "INSERT INTO payment_queue ({$insert_cols}) VALUES (?,?,?,?,?,?,?,?)";
            $stmt = $con->prepare($query);
            $stmt->execute([
                $arr['reference'], $arr['payee_id'], $arr['payment_type'], intval($arr['amount']), $arr['recipient_id'],
                $arr['account_number'] ?? 'account_number', $arr['bank_name'] ?? 'bank_name', json_encode($arr)
            ]);
        } catch (Exception $err) {
            throw new Exception("Ooops!!! Looks like we couldn't queue payment record - " . $err->getMessage());
        }
    }

    public static function insertFloatOne($con, $conn_inventory, $arr, $initiator_name, $initiator_role)
    {
        $payment_queue_cols_arr = [
            'action', 'reference', 'payee_id', 'payment_category', 'amount', 'payee_recipient_id',
            'account_number', 'bank_name', 'payload'
        ];
        $insert_cols = join(",", $payment_queue_cols_arr);
        try {
            // Identify the table to fetch bank details from based on payment type
            $accountdetailsTable = ($arr['payment_type'] === 'float_payment') ? 'float_payment_config' : 'member_cards';

            $initialStmt = $con->prepare("SELECT bank_name, account_number FROM {$accountdetailsTable} WHERE id = ? AND recipient_id = ?");
            $initialStmt->execute([$arr['payee_id'], $arr['recipient_id']]);
            $bank_details = $initialStmt->fetchAll(PDO::FETCH_ASSOC);
            if (count($bank_details) < 1) throw new CustomException(403, "Either recipient_id is invalid or payee has not been profiled on our system");

            // $duplicateStmt = $con->prepare("SELECT action FROM payment_queue WHERE payee_id =? AND amount = ? AND payment_category =? AND created_at > NOW() - INTERVAL '2 hours'");
            $duplicateStmt = $con->prepare("SELECT action FROM payment_queue WHERE payee_id =? AND amount = ? AND payment_category =?");
            $duplicateStmt->execute([$arr['payee_id'], $arr['amount'], $arr['payment_type']]);
            $duplicateTransaction = $duplicateStmt->fetchAll(PDO::FETCH_ASSOC);
            if (count($duplicateTransaction) > 0) throw new CustomException(403, "Possible duplicate transaction detected. Please confirm the authenticity of this transaction");

            // $duplicateStmt2 = $con->prepare("SELECT payee_id FROM harvest_transactions WHERE payee_id =? AND amount = ? AND payment_type =?");
            // $duplicateStmt2->execute([$arr['payee_id'], $arr['amount'], $arr['payment_type']]);
            // $duplicateTransaction2 = $duplicateStmt2->fetchAll(PDO::FETCH_ASSOC);
            // if (count($duplicateTransaction2) > 0) throw new Exception("Possible duplicate transaction detected. Please confirm the authenticity of this transaction");



            $arr['bank_name'] = $bank_details[0]['bank_name'];
            $arr['account_number'] = $bank_details[0]['account_number'];
            $query = "INSERT INTO payment_queue ({$insert_cols}) VALUES (?,?,?,?,?,?,?,?,?)";
            $stmt = $con->prepare($query);
            $stmt->execute([
                $arr['action'], $arr['reference'], $arr['payee_id'], $arr['payment_type'], intval($arr['amount']), $arr['recipient_id'],
                $arr['account_number'] ?? 'account_number', $arr['bank_name'] ?? 'bank_name', json_encode($arr)
            ]);
            PaymentTrackerHelper::insertOne($con, $arr, $initiator_name, $initiator_role);
            
        } catch (Exception $err) {
            throw new Exception("Ooops!!! Looks like we couldn't queue payment record - " . $err->getMessage());
        }
    }

    public static function insertInstantPayment($con, $data, $initiator_name, $initiator_role)
    {
        $amount = $data["amount"];
        $payeeId = $data["payee_id"];
        $bankName = $data["bank_name"];
        $accountNumber = $data["account_number"];
        $classification = $data["classification"];
        $payment_type = getPaymentType($classification);
        $reason = preg_replace('/[\x00-\x1F\x80-\xFF]/', ' ', $data["reason"]);;
        $reference = generateReference($payeeId);

        //echo "Reference generated: ".$reference."\n";

        try {
            if ($payment_type === 'float_payment') {

                //Check if Payee is Frozen
                $isPayeeFrozen = getFloatFreezeStatus($con, $payeeId);
                if ($isPayeeFrozen['freeze_flag'] == 1) throw new CustomException(400, "This personnel is frozen and cannot receive payments.");

                // Fetch bank details from the float_config table based on payeeId
                $initialStmt = $con->prepare("SELECT bank_name, account_number, recipient_id FROM float_payment_config WHERE id = ?");
                $initialStmt->execute([$payeeId]);
                $bankDetails = $initialStmt->fetch(PDO::FETCH_ASSOC);

                if (!$bankDetails || empty($bankDetails['recipient_id'])) {
                    throw new CustomException(400, "Invalid bank details or payee not found in database.");
                }

                // Compare the fetched bank_name and account_number with the ones provided in data
                if ($bankName !== $bankDetails['bank_name'] || $accountNumber !== $bankDetails['account_number']) {
                    throw new CustomException(400, "Bank name or account number does not match the one in Database.");
                }
            }else if ($payment_type === 'wedi_lmr_payment') {
                // Fetch bank details from the Wedi LMR cards table based on payeeId
                $initialStmt = $con->prepare("SELECT bank_name, account_number, recipient_id FROM wedi_lmr_cards WHERE id = ?");
                $initialStmt->execute([$payeeId]);
                $bankDetails = $initialStmt->fetch(PDO::FETCH_ASSOC);

                if (!$bankDetails || empty($bankDetails['recipient_id'])) {
                    throw new CustomException(400, "Invalid bank details or payee not found in database.");
                }

                // Compare the fetched bank_name and account_number with the ones provided in data
                if ($bankName !== $bankDetails['bank_name'] || $accountNumber !== $bankDetails['account_number']) {
                    throw new CustomException(400, "Bank name or account number does not match the one in Database.");
                }
            }else{

                // Fetch bank details from the Member cards table based on payeeId
                $initialStmt = $con->prepare("SELECT bank_name, account_number, recipient_id FROM member_cards WHERE id = ?");
                $initialStmt->execute([$payeeId]);
                $bankDetails = $initialStmt->fetch(PDO::FETCH_ASSOC);
            
                if (!$bankDetails || empty($bankDetails['recipient_id'])) {
                    throw new CustomException(400, "Invalid bank details or payee not found in database.");
                }

                // Compare the fetched bank_name and account_number with the ones provided in data
                if ($bankName !== $bankDetails['bank_name'] || $accountNumber !== $bankDetails['account_number']) {
                    throw new CustomException(400, "Bank name or account number does not match the one in Database.");
                }

                //echo "Bank details match in database \n";
            }

            //echo "About to insert into payment_queue \n";
            // Insert the instant payment into payment_queue
            $query = "INSERT INTO payment_queue (reference, payee_id, payment_category, amount, payee_recipient_id, account_number, bank_name, payload, action) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $con->prepare($query);

            $payload = [
                'reason' => $reason,
                'recipient_id' => $bankDetails['recipient_id'],
                'amount' => $amount,
                'payment_type' => 'instant_payment',
                'payee_id' => $payeeId,
                'reference' => $reference,
                'bank_name' => $bankName,
                'account_number' => $accountNumber,
            ];

            try{
                $stmt->execute([$reference, $payeeId, 'instant_payment', $amount, $bankDetails['recipient_id'], $accountNumber, $bankName, json_encode($payload), $classification]);
            } catch (PDOException $e){
                //echo "Error executing query: ".$e->getMessage()."\n";
                //echo $stmt->errorInfo()." \n";

                throw new CustomException(403, "Unable to insert into the Payment Queue: ".$e->getMessage());
            }            

            //echo "Inserted into payment queue successfully ".$payeeId." Inserting into payment tracker \n";

            PaymentTrackerHelper::insertOne($con, $payload, $initiator_name, $initiator_role);

            //echo "Payment tracker success returning back \n";

            return "Instant payment has been successfully queued.";
        } catch (CustomException $err) {
            throw new CustomException(403, "Ooops!!! Looks like we couldn't queue the instant payment - " . $err->getMessage());
        }
    }



    public static function getPending($con)
    {
        try {
            $stmt = $con->prepare("SELECT payee_id, payload AS payment_info, attempts, last_attempted_at, created_at, extra_info FROM payment_queue ORDER BY created_at");
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $err) {
            throw new Exception("Ooops!!! Looks like we couldn't queue payment record - " . $err->getMessage());
        }
    }

    public static function getPendingPayment($con)
    {
        try {
            $stmt = $con->prepare("
        SELECT 
            pq.payee_id,
            pq.action, 
            pq.payload AS payment_info, 
            pq.attempts, 
            pq.last_attempted_at, 
            pq.created_at, 
            pq.extra_info, 
            pq.approval_flag,
            pt.initiator_name AS owner_name, 
            pt.initiator_role AS owner_role 
        FROM 
            payment_queue pq
        JOIN 
            payment_tracker pt ON pq.payload::JSON->>'reference' = pt.reference_id 
        ORDER BY 
            pq.created_at DESC");

            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $err) {
            throw new Exception("Ooops!!! Looks like we couldn't queue payment record - " . $err->getMessage());
        }
    }


    public static function deleteMany($con, $id_arr)
    {
        try {
            $placeholder_string = join(",", array_fill(0, count($id_arr), "?"));
            $stmt = $con->prepare("DELETE FROM payment_queue WHERE id IN ({$placeholder_string})");
            $stmt->execute($id_arr);
        } catch (Exception $err) {
            throw new Exception("Ooops!!! Looks like we couldn't delete all payment records - " . $err->getMessage());
        }
    }
}
