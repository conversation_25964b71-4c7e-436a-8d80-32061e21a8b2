<?php
require_once (__DIR__.'/../app/controllers/__response_helpers.php');

function callAPI($method, $url, $token, $data = null)
{
    $curl = curl_init();

    if ($data) {
        if (is_array($data) || is_object($data)) {
            curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
        }
    }

    switch ($method)
    {
        case "POST":
            curl_setopt($curl, CURLOPT_POST, 1);

            if ($data)
                curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
            break;
        case "PUT":
            curl_setopt($curl, CURLOPT_PUT, 1);
            break;
        default:
            if ($data)
                $url = sprintf("%s?%s", $url, http_build_query($data));
    }

    // Optional Authentication:
    $authorization = "Authorization: Bearer ".$token; // Prepare the authorisation token
    curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Type: application/json' , $authorization )); // Inject the token into the header


    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);

    $result = curl_exec($curl);

    curl_close($curl);

    log_msg("Wait" .$result);
    return json_decode($result);
}

function log_msg($message)
{
    $message = json_encode($message);
    $message = "[".date(DATE_RFC2822) . "]" . " - $message - ".PHP_EOL;
    $STDERR = fopen("php://stderr", "w");
    fwrite($STDERR, $message);
    fclose($STDERR);
}
function validateToken($token, $permission_id) {
    $data = array();
    $data["app_id"] = "bg_pay";
    if ($permission_id != null) {
        $data["permission_id"] = $permission_id;
    }
    return callAPI("POST", "http://bg-signon.babbangonaapps.com/api/v1/auth/validate_request", $token, $data);
}

function validateUser($permission_id = null) {
    if (!isset($_SERVER["HTTP_AUTHORIZATION"])) {
        showError();
    } else {
        $token = explode(' ', $_SERVER["HTTP_AUTHORIZATION"])[1];
        $result = validateToken($token, $permission_id);

        if ($result->code != 200) {
            showError($result->message, $result->code);
        } else {
            return $result;
        }
    }
}

function showError($apiErrorMessage = "User is not authorized to perform this action", $apiErrorCode = 401) {
    http_response_code($apiErrorCode);
    echo json_encode(setResponse(false, $apiErrorMessage, null, "UNAUTHORIZED"));
    die();
}