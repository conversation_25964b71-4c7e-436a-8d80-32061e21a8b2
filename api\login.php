<?php
require_once '../connect_db.php';
require_once '../constants.php';
require_once '../app/controllers/cors.php';
require_once(__DIR__ . '/../app/models/app_logs.php');
require_once ('auth.php');

cors();

$user = validateUser();

$driver = new DBDriver;
$conn = $driver->connectPgSql(PG_FINANCE_DB_NAME);


$user_data = [
    "user_name" => $user->data->staff_name,
    "access_level_name" => $user->data->config[0]->permission_name,
    "access_level_id" => $user->data->config[0]->configs[0]->config_value
];

$response = [
    'success' => true,
    'message' => "User has successfully logged in",
    'data' => $user_data,
    "error" => null
];

$code = 200;
AppLogs::insertOne($conn, generateLogs($user_data['user_name'], "LOGGED IN", $user_data));

http_response_code($code);
echo json_encode($response);
?>