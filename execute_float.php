<?php

ini_set('memory_limit', '2048M');
ini_set('max_execution_time', '1000');

require_once(__DIR__ . '/jobs/float_cron.php');

$driver = new DBDriver;
$conn_finance = $driver->connectPgSql(PG_FINANCE_DB_NAME);
$conn_inventory = $driver->connectPgSql(PG_INVENTORY_DB_NAME);
$conn_transport = $driver->connectPgSql(PG_TRANSPORTATION_DB_NAME);
date_default_timezone_set('Africa/Lagos');
echo "Version: v0.0.10 \n";


initiate_float_processing($conn_finance, $conn_inventory, $conn_transport);

$conn_inventory = null;
$conn_finance = null;
$conn_transport = null;

?>
