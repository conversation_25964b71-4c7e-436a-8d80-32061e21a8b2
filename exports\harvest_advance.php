<?php
ini_set('max_execution_time', '1000');

require_once(__DIR__.'/../connect_db.php');
require_once(__DIR__.'/../constants.php');

$driver = new DBDriver;
$conn = $driver->connectPgSql(PG_FINANCE_DB_NAME);


date_default_timezone_set('Africa/Lagos');
$dates = date("Y-m-d");
$file = "harvest_advance";//File Name
$filename = $file. "-".$dates;

$id = isset($_GET['id']) ? $_GET['id'] : null;

/*******YOU DO NOT NEED TO EDIT ANYTHING BELOW THIS LINE*******/ 
if(empty($_GET['type'])) {
    http_response_code(400);
    echo json_encode([
        "success"=>false,
        "message"=>"Request failed",
        "data"=>null,
        "error"=>"Request should include export type"
    ]);
    exit;
}   
if($_GET['type'] == 'paid'){
    $sql = "SELECT ik_number, id_loan_size, no_of_bags_marketed,contractual_flag,total_grain_value::integer, net_harvest_advance::integer,COALESCE(amount_paid,0) AS amount_paid, payment_risk,
    net_harvest_advance::integer - COALESCE(amount_paid,0)::integer AS amount_to_be_paid,
        CASE 
           WHEN net_harvest_advance::integer - COALESCE(amount_paid,0) < 0 THEN 'overpaid'
           ELSE 'paid'
        END AS payment_status,
         recipient_id, account_number, bank_name FROM harvest_trust_group_payments
         LEFT OUTER JOIN (SELECT id,recipient_id,account_number,bank_name FROM member_cards WHERE category = 'trust_group') cards ON ik_number = id LEFT OUTER JOIN 
          (SELECT payee_id,SUM(amount) AS amount_paid FROM harvest_transactions GROUP BY payee_id,payment_type HAVING payment_type = 'harvest_advance') trans
           ON  ik_number = payee_id WHERE  net_harvest_advance::integer - COALESCE(amount_paid,0) < 0
           OR (net_harvest_advance <> 0 AND net_harvest_advance::integer - COALESCE(amount_paid,0) = 0)";                                                                                                                                 
}
else{
    $sql = "SELECT ik_number, id_loan_size, no_of_bags_marketed,total_grain_value::integer, net_harvest_advance::integer,COALESCE(amount_paid,0) AS amount_paid, payment_risk,
    CASE 
       WHEN net_harvest_advance = 0 THEN 'zero value'
       WHEN net_harvest_advance - COALESCE(amount_paid,0) = net_harvest_advance THEN 'unpaid'
       ELSE 'underpaid'
       END AS payment_status,
     net_harvest_advance::integer - COALESCE(amount_paid,0)AS amount_to_be_paid,recipient_id, account_number, bank_name FROM harvest_trust_group_payments
      LEFT OUTER JOIN (SELECT id,recipient_id,account_number,bank_name FROM member_cards  WHERE category = 'trust_group') cards ON ik_number = id LEFT OUTER JOIN 
      (SELECT payee_id,SUM(amount) AS amount_paid FROM harvest_transactions GROUP BY payee_id,payment_type HAVING payment_type = 'harvest_advance') trans
       ON  ik_number = payee_id
	   WHERE (net_harvest_advance::integer - COALESCE(amount_paid,0) > 0  OR (net_harvest_advance = 0 AND net_harvest_advance - COALESCE(amount_paid,0) = 0))                                                                                                                              
	   AND NOT ik_number IN (SELECT DISTINCT payee_id FROM payment_queue
       WHERE payment_category = 'harvest_advance')";  

}

if ($id) {
    $cleanIds = preg_replace("/['\"]/", "", $id);
    $idArray = array_map('trim', explode(',', $cleanIds));
    $placeholders = implode(',', array_fill(0, count($idArray), '?'));
    $sql .= " AND ik_number IN ($placeholders)";
}

$stmt = $conn->prepare($sql);

if ($id) {
    $stmt->execute($idArray);
} else {
    $stmt->execute();
}

// $stmt = $conn->prepare($sql);
// $stmt->execute();

//header info for browser
header("Content-Type: application/xls");   
header("Content-Disposition: attachment; filename=$filename.xls");  
header("Pragma: no-cache"); 
header("Expires: 0");
/*******Start of Formatting for Excel*******/   
//define separator (defines columns in excel & tabs in word)
$sep = "\t"; //tabbed character
//start of printing column names as names of MySQL fields
for ($i = 0; $i < $stmt->columnCount(); $i++) {
    $col = $stmt->getColumnMeta($i);
echo $col['name'] . "\t";
}
print("\n");    
//end of printing column names  
//start while loop to get data
    while($row =$stmt->fetch())
    {
        $schema_insert = "";
        for($j=0; $j<$stmt->columnCount();$j++)
        {
            if(!isset($row[$j]))
                $schema_insert .= "NULL".$sep;
            elseif ($row[$j] != "")
                $schema_insert .= "$row[$j]".$sep;
            else
                $schema_insert .= "".$sep;
        }
        $schema_insert = str_replace($sep."$", "", $schema_insert);
        $schema_insert = preg_replace("/\r\n|\n\r|\n|\r/", " ", $schema_insert);
        $schema_insert .= "\t";
        print(trim($schema_insert));
        print "\n";
    }   
?>