<?php
//header("Cache-Control: post-check=0, pre-check=0", false);
//header("Pragma: no-cache");
//header("Cache-Control: no-cache, must-revalidate"); // HTTP/1.1
//header("Expires: Sat, 26 Jul 2097 05:00:00 GMT");


//This function retrieves all of the fields that have not been cleared from HG Transport
function _getRecords($con)
{

    $temp = array();

    #print("Fetching Confirm Threshing Logs <br>");

    $temp = array();

    print("Fetching Scaling Logs <br>");


    $stmt = $con->prepare("select a.hsf_id, a.unique_member_id as memid, a.ik_number, a.bags_received as rcv_bags, a.transaction_date as rcv_date, 
    b.bags_marketed as sc_bags,b.transaction_date as sc_date  from harvest_receiving_record a left outer join harvest_scaling_record b on a.hsf_id =b.hsf_id order by a.unique_member_id asc");
    $stmt->execute();
    $temp = $stmt->fetchAll(PDO::FETCH_ASSOC);

    return $temp;


}

function _getRecordsPivot($con)
{

    $temp = array();

    #print("Fetching Confirm Threshing Logs <br>");

    $temp = array();

    print("Fetching Scaling Logs <br>");


    $stmt = $con->prepare("select a.unique_member_id as memid, sum(a.bags_received) as rcv_bags, sum(b.bags_marketed) as sc_bags, count(a.bags_received) as 
    rcv_count,count(b.bags_marketed) as sc_count from harvest_receiving_record a left outer join harvest_scaling_record b on a.hsf_id =b.hsf_id group by a.unique_member_id order by a.unique_member_id asc");
    $stmt->execute();
    $temp = $stmt->fetchAll(PDO::FETCH_ASSOC);

    return $temp;


}


## This function is used to validate if a given field has exceeded the threshold of 3 days before the field is logged on the backend as HG transport
function _dateDiff($date1, $date2)
{


    $datetime1 = strtotime($date1);
    $datetime2 = strtotime($date2);

    if ($datetime1 == "") {
        $datetime1 = strtotime(date('Y-m-d'));
    }

    if ($datetime2 == "") {
        $datetime2 = strtotime(date('Y-m-d'));
    }

    $secs = $datetime2 - $datetime1;


    (double)$days = $secs / 86400;
    #print("Computing Date Difference => date1 ".$datetime1."  date 2".$datetime2." diff=>".abs($days)."<br>");

    return $days;

}

#Binary search to optimize searching for the processed record of the member in question
function _binarysearch($arr, $keyword, $arr_len)
{

    #print("Binary Search =>  MemID".$keyword);

    if (sizeof($arr) == 0) {

        #print("Binary Search => empty array");

        return -1;
    }

    $x = 0;
    $y = $arr_len - 1;
    $mid = 0;
    while ($x <= $y) {
        $mid = floor($x + ($y - $x) / 2);

        if ($arr[$mid]["memid"] == $keyword) {

            #print("Binary Search => IndexFound =>".$mid." member_id =>".$arr[$mid]["unique_member_id"]." Keyword => ".$keyword);
            return $mid;
        }

        if ($keyword < $arr[$mid]["memid"]) {
            $y = $mid - 1;
        } else {
            $x = $mid + 1;
        }

    }

    return -1;


}

function _processHarvestHGs($con_mysql, $pg_con, $fin_con)
{


    $data_dump = array();
    $data_dump_pivot = array();
    #Retrieve the raw data from the db
    $data_dump = _getRecords($pg_con);

    #Retrieve the raw data from the db
    $data_dump_pivot = _getRecordsPivot($pg_con);


    #print_r($data_dump);

    #Hold important data
    $data = array();
    $dnp_data = array();


    for ($i = 0; $i < sizeof($data_dump); $i++) {

        $umid = $data_dump[$i]['memid'];
        $ik_number = $data_dump[$i]['ik_number'];
        $hsf = $data_dump[$i]['hsf_id'];
        $rcvBags = $data_dump[$i]['rcv_bags'];
        $rcvDate = $data_dump[$i]['rcv_date'];
        $scBags = $data_dump[$i]['sc_bags'];
        $scDate = $data_dump[$i]['sc_date'];

        $mid = _binarysearch($data_dump_pivot, $umid, sizeof($data_dump_pivot));
        $memRecTot = $data_dump_pivot[$mid]['rcv_bags'] ?? 0;
        $memScTot = $data_dump_pivot[$mid]['sc_bags'] ?? 0;
        $memScCount = $data_dump_pivot[$mid]['sc_count'] ?? 0;
        $memRcvCount = $data_dump_pivot[$mid]['rcv_count'] ?? 0;

        $date_diff = _dateDiff($rcvDate, date('Y-m-d H:i:s'));

        $late_scaling = "0";
        $processing_variance = "0";
        $bag_difference = "0";

        if ($date_diff > 3 && $rcvBags > $scBags) {
            $late_scaling = "1";

        }
        if ($date_diff > 3 && $rcvBags < $scBags) {
            $processing_variance = "1";

        }


        $date_diff_2 = _dateDiff($rcvDate, date('Y-m-d H:i:s'));


        if ($memScCount == $memRcvCount && $memRecTot != $memScTot && $rcvBags != $scBags ) {
            $bag_difference = "1";

        }
        $total_flags = $bag_difference + $processing_variance + $late_scaling;


        #print("HSF ".$hsf." Mem ".$umid." Rcv Bags ".$rcvBags." Sc Bags ".$scBags." RcvTot ".$memRecTot." ScTot ".$memScTot." LateThres ".$late_scaling." BagDiff ".$bag_difference."<br>");
//        _updateConfirmThreshing($con_mysql, $hsf, $umid, $rcvBags, $scBags, $rcvDate, $scDate, $memRecTot, $memScTot, $late_scaling, $bag_difference, $processing_variance, $total_flags);
        $row = array();
        $row["hsf"] = $hsf;
        $row["umid"] = $umid;
        $row["ik_number"] = $ik_number;
        $row["rcvBags"] = $rcvBags;
        $row["scBags"] = $scBags;
        $row["rcvDate"] = $rcvDate;
        $row["scDate"] = $scDate;
        $row["memRecTot"] = $memRecTot;
        $row["memScTot"] = $memScTot;
        $row["late_scaling"] = $late_scaling;
        $row["bag_difference"] = $bag_difference;
        $row["processing_variance"] = $processing_variance;
        $row["total_flags"] = $total_flags;

        $data[] = $row;

        if ($total_flags > 0) {
            $dnp_data[] = $row;
        }

    }

    _updateConfirmThreshing($con_mysql, $data);

    _updateDnpData($fin_con, $dnp_data);

}

function _updateDnpData($pg_con, array $dnp_data)
{
    $ids = array_map('get_iks', $dnp_data);
    $ik_list = join(",", $ids);

    $query = "UPDATE harvest_trust_group_payments SET payment_ready = 0 WHERE ik_number in ( $ik_list ) AND all_flags_override = 0";
    $stmt = $pg_con->prepare($query);
    $stmt->execute();
}

function get_iks($data){
    return "'{$data["ik_number"]}'";
}


function _updateConfirmThreshing($con, $data)
{

    $stmt = $con->prepare(get_query($data));
    $stmt->execute();


}

function get_query($data)
{

    //Try to perform the request loop through the list of rows, the first row is the header
    $insert_items = array();
    $headers = array("hsf_id", "unique_member_id", "ik_number", "receiving_bags", "scaling_bags", "receiving_transaction_date", "scaling_transaction_date", "mem_receiving_total", "member_scaling_total", "hg_late_scaling", "hg_bag_difference", "processing_variance", "total_flags");
    foreach ($data as $row) {
        $row_quote = array_map("get_quote", $row);
        $rows_str = join(',', $row_quote);

        $insert_items[] = "($rows_str)";
    }

    $update_items = array();
    foreach ($headers as $header) {
        $update_items[] = "$header = VALUES($header)";
    }

    $values_str = join(', ', $insert_items);
    $headers_str = join(',', $headers);

    $update_str = join(',', $update_items);


    return "INSERT INTO harvest_hgs ({$headers_str}) VALUES $values_str ON DUPLICATE KEY UPDATE $update_str;";
}

function get_quote($str)
{
    return "'$str'";
}


?>
