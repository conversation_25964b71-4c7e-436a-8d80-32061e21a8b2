<?php
require_once (__DIR__.'/../../constants.php');

// set_error_handler(function($errno, $errstr, $errfile, $errline) {
//         if (0 === error_reporting()) {
//             return false;
//         }
//         throw new Exception($errstr);
//     });
    

function setResponse($success,$message,$data,$error){
        return [
         "success"=>$success,
         "message"=>$message,
         "data"=>$data,
         "error"=> $error
        ];
}

function generateLogs($user_id,$user_action,$payload){
        return [
         "user_id"=>$user_id,
         "user_action"=>$user_action,
         "payload"=>json_encode($payload)
        ];
}

function generateReference($id){
        //Modified the function to add a salt and remove the 16 character trim in order to reduce the possibility of hash collisions.
        $salt = uniqid(mt_rand(), true);
        return md5($id . date('Y-m-d H:i:s a') . $salt);

        //return substr(md5($id.date('Y-m-d H:i:s a')), 0,16);
}
function generateUserToken($userObject){
        $encodedUser = base64_encode(json_encode($userObject));
        return md5(date("Y-m-d H:i:s a")).".". $encodedUser.".".md5(APP_SECRET.$encodedUser);
}

function validateUserV1(){
        try{
        $userObject = json_decode(base64_decode(explode(".", explode(' ', $_SERVER["HTTP_AUTHORIZATION"])[1])[1]));
        if(gettype($userObject) === 'object'
        && !empty($userObject->user_name)
        && !empty($userObject->access_level_name)
        && !empty($userObject->access_level_id)
         ) return $userObject;
           }
        catch(Exception $err){
           return false;
        }
}

//Check why amount is multiplied by 100
function extractPayload($paymentPayload){
        try{
        return [
                        "source"=> "balance",
                        "amount"=> $paymentPayload->amount * 100,
                        "recipient"=> $paymentPayload->recipient_id,
                        "reference"=> $paymentPayload->reference,
                        "reason"=> $paymentPayload->reason,
        ];
           
           }
        catch(Exception $err){
           return [
                        "source"=> "balance",
                        "amount"=> null,
                        "recipient"=> null,
                        "reference"=> null,
                        "reason"=> null
           ];
        }
}

?>