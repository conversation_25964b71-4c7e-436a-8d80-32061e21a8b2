<?php

class PaymentTrackerHelper
{

    public static function insertOne($con, $data, $initiator_name, $initiator_role)
    {
        try {
            $query = "INSERT INTO payment_tracker 
                (reference_id, payee_id, payment_type, initiator_name, initiator_role, initiated_at, amount, status, created_at, updated_at) 
                VALUES (?, ?, ?, ?, ?, NOW(), ?, 'PENDING', NOW(), NOW())";

            $stmt = $con->prepare($query);

            $stmt->execute([
                $data['reference'],
                $data['payee_id'],
                $data['payment_type'],
                $initiator_name,
                $initiator_role,
                $data['amount']
            ]);
        } catch (Exception $e) {
            error_log($e->getMessage());

            return 'Error inserting record: ' . $e->getMessage();
        }
    }

    public static function updatePaymentStatus($con, $reference, $status)
    {
        try {
            $query = "UPDATE payment_tracker SET status = ?, updated_at = NOW() WHERE reference_id = ?";

            $stmt = $con->prepare($query);

            $stmt->execute([$status, $reference]);
        } catch (Exception $e) {
            error_log($e->getMessage());

            return 'Error updating record: ' . $e->getMessage();
        }
    }

    public static function updateTransportController($con, $payeeId)
    {
        try {
            $query = "UPDATE transport_controller SET payment_status = 1 WHERE transporter_id = ?";

            $stmt = $con->prepare($query);

            $stmt->execute([$payeeId]);
        } catch (Exception $e) {
            error_log($e->getMessage());

            return 'Error updating record: ' . $e->getMessage();
        }
    }
}
