<?php
require_once(__DIR__ . '/../connect_db.php');
require_once(__DIR__ . '/../scripts/pay_recipient.php');
require_once(__DIR__ . '/../app/controllers/__response_helpers.php');
require_once(__DIR__ . '/../app/models/harvest_tg_payments.php');
require_once(__DIR__ . '/../app/models/payment_queue.php');
require_once(__DIR__ . '/../app/models/app_logs.php');
require_once(__DIR__ . '/../app/models/payment_config.php');
require_once(__DIR__ . '/../app/models/payment_tracker.php');
require_once(__DIR__ . '/../jobs/float_cron.php');

date_default_timezone_set('Africa/Lagos');
ini_set('max_execution_time', '1000');


if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] != 'GET') {
    http_response_code(403);
    echo (json_encode(setResponse(false, "Something went wrong", [], "Request Method Not Allowed")));
    exit;
}

/**
 * One script to execute all payment on the queue
 * Remove the float_payment
 */
(function () {
    try {
        $driver = new DBDriver;
        $conn = $driver->connectPgSql(PG_FINANCE_DB_NAME);
        $conn_inventory = $driver->connectPgSql(PG_INVENTORY_DB_NAME);
        $conn_transport = $driver->connectPgSql(PG_TRANSPORTATION_DB_NAME);

        //Batch id required per payment completed
        $batchId = uniqid('BTH-', true);


        $payments = PaymentQueue::selectPending($conn);
        $responseArray = [];
        $deleteArray = [];

        foreach ($payments as $one_payment) {
            try {
                $payload = json_decode($one_payment['payload']);
                $needed_payload = extractPayload($payload);
                $start = microtime(true);
                $result = payOne($needed_payload);
                $end = microtime(true);

                $elapsed = $end - $start;
                
                $data = [
                    'payload' => $needed_payload,
                    'batch_id' => $batchId,
                    'result' => $result,
                    'elapsed_time' => $elapsed
                ];
                //print_r($data);

                if ($result->status) {
                    HarvestTransactions::insertOne($conn, [
                        "reference" => $needed_payload['reference'],
                        "payee_id" => $payload->payee_id,
                        "recipient_id" => $needed_payload['recipient'],
                        "payment_type" => $payload->payment_type,
                        "amount" => $needed_payload['amount'] / 100,
                        "code" => $result->data->transfer_code ?? 'transfer_code',
                        "transaction_date" => date('Y-m-d H:i:s'),
                        "description" => $result->data->reason ?? 'reason',
                        "batch_id" => $batchId,
                    ]);

                    PaymentTrackerHelper::updatePaymentStatus($conn, $needed_payload['reference'], 'COMPLETED');
                    $deleteArray[] = $one_payment['id'];

                    $responseArray[] = [
                        "success" => true,
                        "account_number" => $payload->account_number,
                        "amount" => $needed_payload['amount'] / 100,
                        "recipient" => $needed_payload['recipient'],
                        "message" => $result->message,
                        "reference" => $needed_payload['reference'],
                        "attempts" => $one_payment['attempts'] + 1,
                    ];
                } else {
                    PaymentQueue::updateOne($conn, $one_payment['id'], [
                        "extra_info" => $result->message,
                        "attempts" => $one_payment['attempts'] + 1,
                        "last_attempted_at" => date('Y-m-d H:i:s'),
                    ]);
                    PaymentTrackerHelper::updatePaymentStatus($conn, $needed_payload['reference'], 'FAILED');

                    $responseArray[] = [
                        "success" => false,
                        "account_number" => $payload->account_number,
                        "amount" => $needed_payload['amount'] / 100,
                        "recipient_id" => $needed_payload['recipient'],
                        "payee_id" => $payload->payee_id,
                        "message" => $result->message,
                        "reference" => $needed_payload['reference'],
                        "attempts" => $one_payment['attempts'] + 1,
                    ];
                }
                AppLogs::insertOne($conn, generateLogs("CRON", "PAYMENT EXECUTED", $data));
            } catch (Exception $e) {
                $responseArray[] = [
                    "success" => false,
                    "reference" => $needed_payload['reference'],
                    "account_number" => $payload->account_number,
                    "message" => $e->getMessage(),
                ];
            }
        }

        // Delete completed payment records from the payment queue
        if (!empty($deleteArray)) {
            PaymentQueue::deleteMany($conn, $deleteArray);
        }

        echo json_encode($responseArray);

        initiate_float_processing($conn, $conn_inventory, $conn_transport);
    } catch (Exception $e) {
        $responseArray[] = json_encode(setResponse(false, "Something went wrong...", [], $e->getMessage()));
        echo json_encode($responseArray);
    }
})();
