<?php

require_once(__DIR__ . '/../api/auth.php');
require_once '../app/controllers/cors.php';
require_once(__DIR__ . '/../constants.php');
require_once(__DIR__ . '/../connect_db.php');
require_once(__DIR__ . '/../app/models/app_logs.php');
require_once(__DIR__.'/../app/models/exception.php' );

cors();

function transportTable($tableName)
{
    try {
        $user = validateUser();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            throw new CustomException(405, "Method Not Allowed. Only POST requests are allowed.");
        }

        $input = json_decode(file_get_contents('php://input'), true);
        
        //Check if the required parameters are set
        $tableName = $input['table_name'] ?? null;
        $id = $input['id'] ?? null;

        if (!$tableName) {
            throw new CustomException(400, "Table name is required");
        }

        $driver = new DBDriver;
        $conn = $driver->connectPgSql(PG_TRANSPORTATION_DB_NAME);
        $conn_finance = $driver->connectPgSql(PG_FINANCE_DB_NAME);

        date_default_timezone_set('Africa/Lagos');
        $dates = date("Y-m-d H:i:s"); // Year-Month-Day Hour:Minute:Second
        $filename = $tableName . "-" . $dates;
        header('Content-Type: text/csv');
        header("Content-Disposition: attachment;filename=\"{$filename}.csv\"");

        $fp = fopen('php://output', 'w');

        //obtain primary key column
        $primaryKeyQuery = "SELECT kcu.column_name FROM information_schema.table_constraints tc JOIN information_schema.key_column_usage kcu ON tc.constraint_name = kcu.constraint_name AND tc.table_name = kcu.table_name WHERE tc.constraint_type = 'PRIMARY KEY' AND tc.table_name = '$tableName'";
        $stmt = $conn->prepare($primaryKeyQuery);
        $stmt->execute();
        $primaryKey = $stmt->fetchColumn();

        if (!$primaryKey) {
            throw new CustomException(400, "Table $tableName does not have a primary key");
        }

        // Dynamically retrieve column names from the table's schema
        if ($tableName == 'shipment' || $tableName == 'id_shipment') {
            $col = "SELECT column_name FROM information_schema.columns WHERE table_name = ? AND column_name NOT LIKE '%template%'
                    ORDER BY CASE WHEN column_name = 'product_package' THEN 1 ELSE 0 END, ordinal_position";
        } else {
            $col = "SELECT column_name FROM information_schema.columns WHERE table_name = ? AND column_name NOT LIKE '%template%'";
        }
        // $col = "SELECT column_name FROM information_schema.columns WHERE table_name = ? AND column_name NOT LIKE '%template%'";
        $stmt = $conn->prepare($col);
        $stmt->execute([$tableName]);
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        fputcsv($fp, $columns);

        $sql = "SELECT " . implode(", ", $columns) . " FROM $tableName";
        if ($id) {
            $cleanIds = preg_replace("/['\"]/", "", $id);
            $idArray = array_map('trim', explode(',', $cleanIds));
            $placeholders = implode(',', array_fill(0, count($idArray), '?'));
            $sql .= " WHERE $primaryKey IN ($placeholders)";
        }
        $stmt = $conn->prepare($sql);
        if ($id) {
            $stmt->execute($idArray);
        } else {
            $stmt->execute();
        }
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            fputcsv($fp, $row);
        }

        // Close the file pointer
        fclose($fp);

        // Logging the user's action
        AppLogs::insertOne($conn_finance, generateLogs($user->data->staff_id, "USER DOWNLOADED DATA FROM $tableName", ""));
    } catch (CustomException $e) {
        http_response_code($e->status ?? 500);
        echo json_encode(['status' => false, 'message' => $e->getMessage()]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['status' => false, 'message' => $e->getMessage()]);
    }
}

transportTable('shipment')
?>