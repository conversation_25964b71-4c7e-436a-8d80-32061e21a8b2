<?php
require_once '../../connect_db.php';
require_once '../../constants.php';
require_once '../../app/controllers/cors.php';

cors();

$data = @file_get_contents('php://input');
$event = json_decode($data, true);

$amount = "";
//$ref = $dataarray["data"]["reference"];
//$status = $dataarray["data"]["status"];
//$amount = $dataarray["data"]["amount"];

$driver = new DBDriver;
$conn_inventory = $driver->connectPgSql(PG_FINANCE_DB_NAME);
try {
    $sql = "INSERT into payment_portal_test_data(test_data)
 VALUES (?)";

    $stmt = $conn_inventory->prepare($sql);

    $result = $stmt->execute([$data]);
}
catch(Exception $e) {
    echo 'Message: ' .$e->getMessage();
}




http_response_code(200);


?>