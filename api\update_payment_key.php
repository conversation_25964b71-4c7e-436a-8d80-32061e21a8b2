<?php
require_once (__DIR__.'/../constants.php');
require_once (__DIR__.'/../connect_db.php');
require_once (__DIR__.'/../app/controllers/__payment_validations.php');
require_once (__DIR__.'/../api/auth.php');
require_once (__DIR__.'/../app/models/exception.php');
require_once (__DIR__.'/../app/controllers/cors.php');
require_once (__DIR__.'/../app/models/app_logs.php');

cors();

$user = validateUser();
$json = file_get_contents('php://input');
$data = json_decode($json, true);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $user = validateUser();
    
    if (!$user) {
        $response = get_response(false, "Invalid request. User could not be authenticated", null);
        $code = 401;
        exit;
    }
    
    $data = json_decode(file_get_contents('php://input'), true);
    $paystack_api_key = $data['paystack_api_key'] ?? null;
    $current_date = date('Y-m-d H:i:s');
    
    if (!$paystack_api_key) {
        $response = get_response(false, "Paystack api key is required", null);
        $code = 401;
        exit;
    }
    
    try {
        $driver = new DBDriver;
        $conn_finance = $driver->connectPgSql(PG_FINANCE_DB_NAME);
        
        $sql = "UPDATE payment_secrets SET value = :new_api_key, updated_at = :current_date WHERE key = 'PAYSTACK_API_KEY'";
        $stmt = $conn_finance->prepare($sql);

        $stmt->bindParam(':new_api_key', $paystack_api_key, PDO::PARAM_STR);
        $stmt->bindParam(':current_date', $current_date, PDO::PARAM_STR);

        if($stmt->execute()) {
            $response = get_response(true, 'API key updated successfully', null);
            $code = 200;
            AppLogs::insertOne($conn_finance, generateLogs($user->data->staff_id, "USER UPDATED PAYSTACK SECRET KEY", $data));
        } else {
            $response = get_response(false, 'Failed to update API key', null);
            $code = 500;
        }
        
    } catch (Exception $e) {
        $response = get_response(false, 'Database error: ' . $e->getMessage(), $e->getCode());
        $code = 500;
    }
    
} else {
    $response = get_response(false, "Method not allowed", null);
    $code = 405;
    
}






http_response_code($code);
echo json_encode($response);

function get_response($status, $message, $error)
{
    return [
        'success' => $status,
        'message' => $message,
        'error' => $error
    ];
}

?>