<?php
require_once(__DIR__ ."/../app//controllers/__data_helpers.php");
require_once(__DIR__ ."/../app/controllers/Logger.php");
require_once(__DIR__ ."/../app/models/harvest_tg_payments.php");
require_once(__DIR__ ."/../app/models/ms_playbook_dnp.php");
require_once(__DIR__ ."/../app/models/harvest_member_payments.php");
require_once(__DIR__ ."/../app/controllers/__data_helpers.php");


/**
 * Cron 3 computes the records on the harvest_trust_group_payment table, it is a pivot of the data on the harvest member payment in the sense that it sums records on
 * a member level and converts it to tg level. it gives the final value of what should be paid out as harvest advance for trustgroups as well as default value at 
 * 
*/


function executeCron3( $conn_3, $conn_4, $config){
    $start = microtime(true);
    $logArray = [];
    $insertArray = [];
    $memberUpdates = [];
    //$dnpGroups = [];

    echo  date('Y-m-d H:i:s.u')." 🎶🎶🎶  =====CRON 3 STARTED==== 🎶🎶🎶 <br /> \n";
      try{
        echo  date('Y-m-d H:i:s.u')." ✋✋......Now fetching Initial Data... ✋✋ <br /> \n";
        $tgs_from_members = MemberPayments::selectGrouped($conn_3);
        $trust_groups = TGPayments::selectMany($conn_3, array_column($tgs_from_members, 'ik_number'));
        //$do_not_pay_list = MSDoNotPay::selectMany($conn_4);
       echo  date('Y-m-d H:i:s.u')." ✔✔✔✔ ......Done fetching Initial Data...✔✔✔✔ <br /> \n";


       foreach($trust_groups as $thisGroup){
        $thisGroup['season'] = $config->season_config; // this line will be removed if manual copying includes season already

       // Traverse selected trust groups and update their records with appropriate values from member payments table
       if(isset($tgs_from_members[$thisGroup['ik_number']])){

        // Add this IK number to the ones to be updated on MEMBER payments (transferred_flag)
        $memberUpdates[] = $thisGroup['ik_number'];

            $foundObject = $tgs_from_members[$thisGroup['ik_number']];
            $contractualFlag = $foundObject->bags / ($foundObject->field_size == 0 ? 1 : $foundObject->field_size)
                                == $config->contractual_threshold_config ? 0 : 1;

            $minPayout = getMinPayout($thisGroup,$config);
            $maxPayout = getMaxPayout($thisGroup,$config);
            $pricePerHectare = $foundObject->harvest_advance / ($foundObject->field_size == 0 ? 1 : $foundObject->field_size);



            $payoutFlag = $pricePerHectare >= $minPayout && $pricePerHectare <= $maxPayout ?  0 : 1;


            // if(onDNPList($do_not_pay_list, $thisGroup)) {
            //   $thisGroup['shp_dnp'] = 1;
            //   $dnpGroups[] = $thisGroup['ik_number'];
            // }

            $thisGroup['id_loan_size'] = $foundObject->field_size;
            $thisGroup['no_of_bags_marketed'] = $foundObject->bags;
            $thisGroup['contractual_flag'] = $contractualFlag;
            $thisGroup['member_failed_expectation'] = $foundObject->expectation  > 0 || $contractualFlag == 1 ? 1 : 0;
            $thisGroup['net_harvest_advance'] = $foundObject->net_harvest_advance < 0 ? 0 : $foundObject->net_harvest_advance;
            $thisGroup['total_harvest_advance'] = $foundObject->harvest_advance < 0 ? 0 : $foundObject->harvest_advance;
            $thisGroup['total_grain_value'] = $foundObject->grain_value;
            $thisGroup['loan_before_harvest'] = $foundObject->total_loan;
            $thisGroup['total_cost'] = $foundObject->total_cost;
            $thisGroup['misc_account'] = $foundObject->total_misc;
            $thisGroup['updated_flag'] = 1;
            $thisGroup['max_payout_flag'] = $payoutFlag;
            $thisGroup['max_payout_size_flag'] = $foundObject->net_harvest_advance > $config->max_payout_size_config ? 1 : 0;
            $thisGroup['financial_default'] = $foundObject->harvest_advance < 0 ? $foundObject->net_harvest_advance : 0;
            $insertArray[] = $thisGroup;
       }
       }
       echo  date('Y-m-d H:i:s.u')." ✋✋......Now running db updates and inserts...✋✋ <br /> \n";
        TGPayments::insertMany($conn_3, $insertArray, 2400);
        MemberPayments::updateMany($conn_3, $memberUpdates, ["transferred_flag" => 1]);
        echo  date('Y-m-d H:i:s.u')." ✔✔✔✔......Done running db updates and inserts... ✔✔✔✔ <br /> \n";

        // CALCULATE AND UPDATE PAYMENT FLAGS
        $tgs_for_payment = TGPayments::selectForPayment($conn_3);
        $no_risk_group = [];
        $low_override_group = [];
        // $medium_flags_group = [];
        $low_contractual_group = [];
        $high_maxpay_group = [];
        $no_pay_group = [];

        foreach($tgs_for_payment as $oneGroup){
            if(
                  $oneGroup['contractual_flag'] == 0     &&
                  $oneGroup['shp_dnp'] == 0              &&
                  $oneGroup['finance_dnp'] == 0          &&
                  $oneGroup['tech_dnp'] == 0             &&
                  $oneGroup['max_payout_flag'] == 0      
                ){
                    $no_risk_group[] = $oneGroup['ik_number'];
                  }
            else if(
            (
            $oneGroup['contractual_flag'] == 1 || $oneGroup['shp_dnp'] == 1 ||
            $oneGroup['finance_dnp'] == 1      || $oneGroup['max_payout_flag'] == 1 || $oneGroup['tech_dnp'] == 1
              )   && $oneGroup['all_flags_override'] == 1
            )   
            {
              $low_override_group[] = $oneGroup['ik_number'];
            }
            else if (
              (
                  ($oneGroup['finance_dnp'] + $oneGroup['shp_dnp'] + $oneGroup['tech_dnp']) - 
                  ($oneGroup['finance_dnp_override'] + $oneGroup['shp_dnp_override'] + $oneGroup['tech_dnp_override']) >= 1
              )
          ) {
              $no_pay_group[] = $oneGroup['ik_number'];
          }
            else if($oneGroup['contractual_flag'] == 1) $low_contractual_group[] = $oneGroup['ik_number'];
            else if($oneGroup['max_payout_flag'] == 1)  $high_maxpay_group[] = $oneGroup['ik_number'];
            else $no_pay_group[] = $oneGroup['ik_number'];
        }

        TGPayments::updateMany($conn_3, $no_risk_group, ["payment_ready"=>1,"payment_risk"=>'NONE']);
        TGPayments::updateMany($conn_3, $low_override_group, ["payment_ready"=>1,"payment_risk"=>'LOW-OVERRIDE']);
        // TGPayments::updateMany($conn_3, $medium_flags_group, ["payment_ready"=>1,"payment_risk"=>'MEDIUM-FLAGS']);
        TGPayments::updateMany($conn_3, $low_contractual_group, ["payment_ready"=>1,"payment_risk"=>'LOW-CONTRACTUAL']);
        TGPayments::updateMany($conn_3, $high_maxpay_group, ["payment_ready"=>1,"payment_risk"=>'HIGH-MAX_PAYOUT']);
        TGPayments::updateMany($conn_3, $no_pay_group, ["payment_ready"=>0,"payment_risk"=>'HIGH-DNP']);


      // STRICTLY LOGS
      //LOG 10 RECORDS FOR EACH CATEGORY
    for($i=0; $i < 10; $i++){
      if($i >= count($tgs_from_members)) break;
      $logArray[] = getLogString("CRON 3",  array_keys($tgs_from_members)[$i]. " - Was initially fetched from MEMBER payments");
    }
    for($i=0; $i < 10; $i++){
      if($i >= count($trust_groups)) break;
      $logArray[] = getLogString("CRON 3",  $trust_groups[$i]['ik_number']. " - Was initially fetched from TG payments");
    }
    for($i=0; $i < 10; $i++){
      if($i >= count($insertArray)) break;
      $logArray[] = getLogString("CRON 3",  $insertArray[$i]['ik_number']. " - Was updated on TG payments");
    }
    // for($i=0; $i < 10; $i++){
    //   if($i >= count($dnpGroups)) break;
    //   $logArray[] = getLogString("CRON 3",  $dnpGroups[$i]. " - Was found in DNP List and flag was updated on TG payments");
    // }

      $stop = microtime(true);
      $elapsed = $stop - $start;
      $logArray[] = getLogString("CRON 3",  "🎵🎵🎵 Bravo!!!! CRON 3 ran successfully in ". $elapsed. " seconds 🎵🎵🎵");
      $logArray[] = getLogString("CRON 3"  ,'🔊🔊🔊🔊🔊 - JOB SUMMARY - 🔊🔊🔊🔊🔊');
      $logArray[] = getLogString("CRON 3",  '✍✍ - '. count($tgs_from_members). "  Total trust group records fetched from harvest MEMBER payments===");
      $logArray[] = getLogString("CRON 3",  '✍✍ - ' . count($trust_groups). "   Total trust group records fetched from harvest TG payments===");
      //$logArray[] = getLogString("CRON 3",  '✍✍ - '. count($do_not_pay_list). " -  DNP records fetched from bulkbana_mkt===");
      $logArray[] = getLogString("CRON 3",  '✍✍ - '. count($insertArray). " -   record(s) updated in TG payments table===");  
      $logArray[] = getLogString("CRON 3",  "👏👏👏 CRON 3 SAYS BYE...👏👏👏👏");
        // END OF LOGS

      foreach($logArray as $log){
       echo $log;
      }
       }
        catch(Exception $err){
            foreach($logArray as $log){
            echo $log;
            }
            echo "🙈🙈🙈...Something went wrong. CRON 3 failed: 🙈🙈🙈 ". $err->getMessage();
        }
}
?>