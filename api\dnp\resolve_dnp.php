<?php
require_once '../../connect_db.php';
require_once '../../constants.php';
require_once '../../app/controllers/cors.php';
require_once('../auth.php');
require_once(__DIR__ . '/../../app/controllers/__response_helpers.php');
require_once(__DIR__ . '/../../app/models/app_logs.php');
require_once("../../app/controllers/__data_helpers.php");

cors();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $user = validateUser();

    $json = file_get_contents('php://input');
    $data = json_decode($json, true);

    $id = $data['id'] ?? null;
    $ik_number = $data['ik_number'] ?? null;
    $comment_resolved = $data['comment'] ?? null;

    if (!$id || !$ik_number || !$comment_resolved) {
        http_response_code(400);
        echo json_encode(setResponse(false, "Missing required fields", [], "Required fields: id, ik_number, comment"));
        exit();
    }

    try {
        $driver = new DBDriver;
        $conn_finance = $driver->connectPgSql(PG_FINANCE_DB_NAME);

        // Fetch dnp_category from dnp_logs table
        $select_category_query = "SELECT dnp_category FROM dnp_logs WHERE dnp_id = :id";
        $stmt_category = $conn_finance->prepare($select_category_query);
        $stmt_category->bindParam(':id', $id);
        $stmt_category->execute();
        $dnp_category = $stmt_category->fetchColumn();

        if (!$dnp_category) {
            throw new Exception("DNP log not found for the given ID");
        }

        $resolved_by = $user->data->staff_id;
        $date_solved = date('Y-m-d H:i:s');
        $updated_at = $date_solved;

        // Update dnp_logs table
        $update_logs_query = "UPDATE dnp_logs SET comment_solved = :comment_solved, resolved_by = :resolved_by, date_solved = :date_solved, updated_at = :updated_at, status = 1 WHERE dnp_id = :id";
        $stmt_logs = $conn_finance->prepare($update_logs_query);
        $stmt_logs->bindParam(':comment_solved', $comment_resolved);
        $stmt_logs->bindParam(':resolved_by', $resolved_by);
        $stmt_logs->bindParam(':date_solved', $date_solved);
        $stmt_logs->bindParam(':updated_at', $updated_at);
        $stmt_logs->bindParam(':id', $id);
        $result_logs = $stmt_logs->execute();

        if ($result_logs) {
            // Update the appropriate column in harvest_trust_group_payments table based on dnp_category
            if ($dnp_category === 'Finance DNP') {
                $update_payment_query = "UPDATE harvest_trust_group_payments SET finance_dnp_override = 1 WHERE ik_number = :ik_number";
            } else {
                $update_payment_query = "UPDATE harvest_trust_group_payments SET tech_dnp_override = 1 WHERE ik_number = :ik_number";
            }

            $stmt_payment = $conn_finance->prepare($update_payment_query);
            $stmt_payment->bindParam(':ik_number', $ik_number);
            $result_payment = $stmt_payment->execute();

            if ($result_payment) {
                AppLogs::insertOne($conn_finance, generateLogs($resolved_by, "DNP RESOLVED", $data));
                http_response_code(200);
                echo json_encode(setResponse(true, "DNP log has been resolved", [], ""));
            } else {
                throw new Exception("Failed to update the payment override in harvest_trust_group_payments");
            }
        } else {
            throw new Exception("Failed to update dnp_logs");
        }
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(setResponse(false, "Something went wrong", [], $e->getMessage()));
    }

} else {
    http_response_code(405);
    echo json_encode(setResponse(false, "Method not allowed", [], "Method not allowed"));
}
