<?php
require_once(__DIR__ . "/../constants.php");
require_once(__DIR__ . "/../connect_db.php");
require_once(__DIR__ . "/../app/models/harvest_transactions.php");
require_once(__DIR__ . "/../app/models/payment_references.php");
require_once(__DIR__ . "/../app/models/payment_secrets.php");
require_once(__DIR__ . "/../app/models/harvest_transactions.php");
require_once(__DIR__ . "/../app/controllers/__response_helpers.php");

function payOne($payload)
{
        $driver = new DBDriver;
        $con = $driver->connectPgSql(PG_FINANCE_DB_NAME);

        try {
                $configs = PaymentSecrets::selectMany($con);

                $post_fields = http_build_query($payload);

                // Now make a request to Paystack Api                                                                                                 
                $ch = curl_init(PAYSTACK_BASE_URL . '/transfer');
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
                curl_setopt($ch, CURLOPT_TIMEOUT, 0);
                curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $post_fields);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                        "Authorization: Bearer " . trim($configs->PAYSTACK_API_KEY),
                        "Cache-Control: no-cache",
                ));
                $response = curl_exec($ch);

                if (curl_errno($ch)) {
                        // Log cURL error
                        error_log("cURL Error (" . curl_errno($ch) . "): " . curl_error($ch));
                        return setResponse(false, "Something went wrong", [], curl_errno($ch));
                }
                return json_decode($response);
        } catch (Exception $e) {
                return ["status" => false, "error" => $e->getMessage()];
        }
}

?>
