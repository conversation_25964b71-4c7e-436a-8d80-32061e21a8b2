<?php
class AppLogs{
    public static function insertOne($con, $insertArray){
        $app_logs_cols_arr = [
            
        ];
        $insert_cols = join(",", array_keys($insertArray));
        $placeholder_string = join(',' , array_fill(0, count($insertArray),'?'));

        try{
                $query = "INSERT INTO app_logs (user_id,user_action,payload) VALUES (?,?,?)";
                $stmt = $con->prepare($query);
                $stmt->execute([$insertArray['user_id'],$insertArray['user_action'],$insertArray['payload']]);
            }
        
        catch(Exception $err){
            throw new Exception("Ooops!!! Looks like we couldn't insert all App Logs - ". $err->getMessage());
        }
    }
}
?>