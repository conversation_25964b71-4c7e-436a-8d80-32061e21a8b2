<?php
require_once '../../connect_db.php';
require_once '../../constants.php';
require_once '../../app/controllers/cors.php';
require_once('../auth.php');
require_once(__DIR__ . '/../../app/controllers/__response_helpers.php');
require_once(__DIR__ . '/../../app/models/app_logs.php');
require_once("../../app/controllers/__data_helpers.php");

cors();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $user = validateUser();

    $json = file_get_contents('php://input');
    $data = json_decode($json, true);

    $ik_number = $data['ik_number'] ?? null;
    $dnp_category = $data['category'] ?? null;
    $hub_id = $data['hub_id'] ?? null;
    $reason = $data['dnp_reason_id'] ?? null;
    $comment = $data['comment'] ?? null;

    if (!$ik_number || !$dnp_category || !$hub_id || !$reason || !$comment) {
        http_response_code(400);
        echo json_encode(setResponse(false, "Missing required fields", [], "Required fields: ik_number, category, hub_id, reason, comment"));
        exit();
    }

    if (!in_array($dnp_category, DNP_CATEGORY)) {
        http_response_code(400);
        echo json_encode(setResponse(false, "Invalid Dnp category", [], ""));
        exit(); 
    }

    $ikN = [$ik_number];
    $tg_leader = getTGLeaders($ikN);
    if (!$tg_leader) {
        http_response_code(400);
        echo json_encode(setResponse(false, "Something went wrong", [], "Invalid IK Number"));
        exit();
    }

    try {
        $driver = new DBDriver;
        $conn_finance = $driver->connectPgSql(PG_FINANCE_DB_NAME);
        $conn_planning = $driver->connectPgSql(PG_PLANNING_DB_NAME);

        $hubId = [$hub_id];
        $hub = getTGHubs($conn_planning, $hubId);
        if (!$hub) {
            throw new Exception("Invalid Hub ID");
        }

        $dnp_id = "DNP-" . str_pad(rand(0, 99999999), 8, '0', STR_PAD_LEFT);
        $staff_id = $user->data->staff_id;
        $date_logged = date('Y-m-d H:i:s');
        $created_at = $date_logged;
        //$updated_at = $date_logged;

        $query = "
            INSERT INTO dnp_logs (dnp_id, ik_number, dnp_category, hub_id, dnp_reason_id, comment_created, date_logged, created_at, created_by)
            VALUES (:dnp_id, :ik_number, :dnp_category, :hub_id, :dnp_reason_id, :comment, :date_logged, :created_at, :created_by)
        ";

        $stmt = $conn_finance->prepare($query);
        $stmt->bindParam(':dnp_id', $dnp_id);
        $stmt->bindParam(':ik_number', $ik_number);
        $stmt->bindParam(':dnp_category', $dnp_category);
        $stmt->bindParam(':hub_id', $hub_id);
        $stmt->bindParam(':dnp_reason_id', $reason);
        $stmt->bindParam(':comment', $comment);
        $stmt->bindParam(':date_logged', $date_logged);
        $stmt->bindParam(':created_at', $created_at);
        $stmt->bindParam(':created_by', $staff_id);

        $result = $stmt->execute();

        if ($result) {
            // Log the DNP action
            AppLogs::insertOne($conn_finance, generateLogs($staff_id, "DNP LOGGED", $data));

            //update the dnp field in the `harvest_trust_group_payment` table
            if ($dnp_category === 'Finance DNP') {
                $update_query = "UPDATE harvest_trust_group_payments SET finance_dnp = 1, finance_dnp_override = 0 WHERE ik_number = :ik_number";
            } else {
                $update_query = "UPDATE harvest_trust_group_payments SET tech_dnp = 1, tech_dnp_override = 0 WHERE ik_number = :ik_number";
            }

            $update_stmt = $conn_finance->prepare($update_query);
            $update_stmt->bindParam(':ik_number', $ik_number);

            $update_result = $update_stmt->execute();

            if ($update_result) {
                http_response_code(200);
                echo json_encode(setResponse(true, "DNP has been logged", [], ""));
            } else {
                throw new Exception("Failed to update finance dnp");
            }
        } else {
            throw new Exception("Failed to log DNP");
        }
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(setResponse(false, "Something went wrong", [], $e->getMessage()));
    }
    
} else {
    http_response_code(405);
    echo json_encode(setResponse(false, "Method not allowed", [], "Method not allowed"));
}
