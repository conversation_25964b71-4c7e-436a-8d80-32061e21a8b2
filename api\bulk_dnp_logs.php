<?php

require_once(__DIR__ . '/../../constants.php');
require_once(__DIR__ . '/../../connect_db.php');
require_once(__DIR__ . '/../../app/controllers/__payment_validations.php');
require_once(__DIR__ . '/../auth.php');
require_once(__DIR__ . '/../../app/models/exception.php');
require_once(__DIR__ . '/../../app/controllers/cors.php');
require_once(__DIR__ . '/../../app/models/app_logs.php');

ini_set('max_execution_time', '1000');

cors();

(function () {
    try {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            throw new CustomException(403, "Request Method Not Allowed");
        }

        $user = validateUser();
        $driver = new DBDriver();
        $conn = $driver->connectPgSql(PG_FINANCE_DB_NAME);

        $successMessages = [];
        $errorMessages = [];

        if ($_FILES['csv_file']['error'] === UPLOAD_ERR_OK) {
            $csvFile = $_FILES['csv_file']['tmp_name'];
            if (($handle = fopen($csvFile, "r")) !== false) {
                $expectedColumns = ['ik_number', 'dnp_category', 'hub_id', 'reason', 'comment'];
                $csvColumns = fgetcsv($handle);
                $csvColumns = array_map('trim', $csvColumns);

                if ($csvColumns !== $expectedColumns) {
                    throw new CustomException(400, "CSV columns do not match the expected columns or are not in the correct order");
                }

                if (feof($handle)) {
                    throw new CustomException(400, "The CSV file is empty");
                }

                $rowCounter = 0;

                while (($data = fgetcsv($handle, 1000, ",")) !== false) {
                    $rowCounter++;

                    if ($rowCounter > 200) {
                        $errorMessages[] = [
                            'data' => 'N/A',
                            'message' => 'CSV file has more than 200 rows. Only the first 200 have been processed.',
                        ];
                        break;
                    }

                    // Extracting and validating payment data from CSV line
                    $ik_number = $data[0];
                    $dnp_category = $data[1];
                    $hub_id = $data[2];
                    $reason = $data[3];
                    $comment = $data[4];

                    try {
                        $conn_finance = $driver->connectPgSql(PG_FINANCE_DB_NAME);
                        $staff_id = $user->data->staff_id;
                        $date_logged = date('Y-m-d H:i:s');

                        // Inserting the DNP data into the logs table
                        $insert_query = "
                            INSERT INTO dnp_logs (ik_number, dnp_category, hub_id, reason, comment_created, date_logged, created_at, updated_at)
                            VALUES (:ik_number, :dnp_category, :hub_id, :reason, :comment, :date_logged, :date_logged, :date_logged)
                        ";

                        $insert_stmt = $conn_finance->prepare($insert_query);
                        $insert_stmt->bindParam(':ik_number', $ik_number);
                        $insert_stmt->bindParam(':dnp_category', $dnp_category);
                        $insert_stmt->bindParam(':hub_id', $hub_id);
                        $insert_stmt->bindParam(':reason', $reason);
                        $insert_stmt->bindParam(':comment', $comment);
                        $insert_stmt->bindParam(':date_logged', $date_logged);

                        if ($insert_stmt->execute()) {
                            // Updating the finance_dnp field in the harvest_trust_group_payment table
                            $update_query = "UPDATE harvest_trust_group_payment SET finance_dnp = 1 WHERE ik_number = :ik_number";
                            $update_stmt = $conn_finance->prepare($update_query);
                            $update_stmt->bindParam(':ik_number', $ik_number);

                            if ($update_stmt->execute()) {
                                $successMessages[] = "DNP logged and updated for ik_number: $ik_number";
                            } else {
                                $errorMessages[] = [
                                    'ik_number' => $ik_number,
                                    'message' => "Failed to update finance_dnp for ik_number: $ik_number",
                                ];
                            }
                        } else {
                            $errorMessages[] = [
                                'ik_number' => $ik_number,
                                'message' => "Failed to log DNP for ik_number: $ik_number",
                            ];
                        }
                    } catch (Exception $e) {
                        // Log every error in errorMessages
                        $errorMessages[] = [
                            'ik_number' => $ik_number,
                            'message' => $e->getMessage(),
                        ];
                    }
                }

                fclose($handle);
            } else {
                throw new CustomException(400, "Error reading CSV file");
            }
        } else {
            throw new CustomException(400, "Error uploading CSV file");
        }

        // Return the response with success and error messages
        http_response_code(201);
        echo json_encode([
            'status' => true,
            'message' => 'Bulk payment request processed',
            'success_messages' => $successMessages,
            'error_messages' => $errorMessages,
        ]);

        // Logging the bulk action
        AppLogs::insertOne($conn, generateLogs($user->data->staff_id, "SUBMITTED BULK INSTANT PAYMENTS", $successMessages));

    } catch (CustomException $e) {
        // Catch all errors and add them to errorMessages
        $errorMessages[] = [
            'message' => $e->getMessage(),
        ];

        http_response_code($e->status ?? 500);
        echo json_encode([
            'status' => false,
            'message' => 'Something went wrong... Payment not queued',
            'error_messages' => $errorMessages,
        ]);
    }
})();

?>
