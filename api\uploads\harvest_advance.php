<?php
require_once (__DIR__.'/../../constants.php');
require_once (__DIR__.'/../../connect_db.php');
require_once (__DIR__.'/../auth.php');
require_once (__DIR__.'/../../app/models/exception.php');
require_once (__DIR__.'/../../app/models/harvest_tg_payments.php');
require_once (__DIR__.'/../../app/controllers/cors.php');
require_once (__DIR__.'/../../app/models/app_logs.php');

ini_set('max_execution_time', '1000');

cors();

(function(){
    try{
        $user = validateUser();
    
        if($_SERVER['REQUEST_METHOD'] != 'POST') throw new CustomException(403,"Request Method Not Allowed");

        $fileArray = $_FILES['trust_groups'] ?? [];

        if(empty($fileArray)) throw new CustomException(400,"Please upload a valid CSV file for payment");

        $fileExt = strtolower(pathinfo($fileArray['name'],PATHINFO_EXTENSION));

        if($fileExt !== 'csv') throw new CustomException(400,"Please upload a valid CSV file for payment");

        $fileContent = $fileArray['tmp_name'];

        $trustGroups = [];
         
        if (($handle = fopen($fileContent, "r")) !== FALSE) {
            while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
              $trustGroups[] = $data[0];
            }
            fclose($handle);
        }

        $responseArray = [];

        if(!empty($trustGroups)) 
        {
            $driver = new DBDriver;
            $conn = $driver->connectPgSql(PG_FINANCE_DB_NAME);    
            $responseArray = TGPayments::selectFromFile($conn, $trustGroups);
        }

        $response = setResponse(true,"All trust groups successfully retrieved",$responseArray,null);
        http_response_code(200);
        echo json_encode($response);        

        AppLogs::insertOne($conn,generateLogs($user->data->staff_id,"SUBMITTED FILE BULK PAYMENT - ". $fileArray['name'] , $trustGroups));
    }
    catch(CustomException $e){
        http_response_code($e->status ?? 500);
        echo json_encode(setResponse(false,"Something went wrong..... ",[], $e->getMessage()));
    }
})()


?>