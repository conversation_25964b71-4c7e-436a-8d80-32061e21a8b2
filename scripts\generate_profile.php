<?php
require_once(__DIR__ . "/../constants.php");
require_once(__DIR__ . "/../connect_db.php");
require_once(__DIR__ ."/../app/models/member_cards.php");
require_once(__DIR__ ."/../app/models/payment_secrets.php");
require_once(__DIR__ ."/../app/models/exception.php");
require_once(__DIR__ ."/../app/controllers/__response_helpers.php");
require_once(__DIR__ ."/../data/bank_codes.php");
require_once(__DIR__ ."/../app/controllers/Logger.php");

ini_set('max_execution_time', '300');

if(isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] == 'GET') {
    // $user = validateUser();
    // if(!$user){
    //     http_response_code(401);
    //     echo(json_encode(setResponse(false,"Something went wrong", [], "Invalid request. User could not be authenticated")));
    //     exit;
    // }
    }
    
    
    if(isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] != 'GET') {
        http_response_code(403);
        echo(json_encode(setResponse(false,"Something went wrong", [], "Request Method Not Allowed")));
        exit;
    }

(function($bank_list){
        try{
                $driver = new DBDriver;
                $con = $driver->connectPgSql(PG_FINANCE_DB_NAME);

                $pending_users = MemberCards::selectPending($con);
                $configs = PaymentSecrets::selectMany($con);
                $responseArray = [];

                // LOOP THROUGH MEMBER CARDS LIST GENERATING ids FOR EACH AND UPDATING THE MEMBER CARDS TABLE ACCORDINGLY
                foreach($pending_users as $one_user){
                    $result = generateID($one_user,$bank_list, $configs);
                    if($result->status){
                        MemberCards::updateOne($con, $one_user['id'],
                        [
                            "recipient_id"=>$result->data->recipient_code,
                            "status_message"=>$result->message,
                            "updated_at"=>date('Y-m-d H:i:s'),
                            "profiling_attempts" => $one_user['profiling_attempts'] + 1
                        ]);
                        $responseArray[] = [
                            "success"=>true,
                            "id"=> $one_user["id"],
                            "account_number"=> $one_user["account_number"],
                            "bank_name"=> $one_user["bank_name"],
                            "message"=>$result->message,
                            "generated_id"=>$result->data->recipient_code,
                            "attempts"=>$one_user["profiling_attempts"] + 1
                            ];

                    }
                    else{
                        MemberCards::updateOne($con, $one_user['id'], 
                        ["status_message"=>$result->message,
                         "updated_at"=> date('Y-m-d H:i:s'),
                         "profiling_attempts" => $one_user['profiling_attempts'] + 1
                        ]);
                        $responseArray[] = [
                            "success"=>false,
                            "id"=> $one_user["id"],
                            "account_number"=> $one_user["account_number"],
                            "bank"=> $one_user["bank_name"],
                            "bank_code"=> $bank_list[$one_user['bank_name']] ?? $bank_list['default'],
                            "message"=>$result->message,
                            "attempts"=>$one_user["profiling_attempts"] + 1
                            ];
                    }
                    }
                }
        catch(Exception $e){
            $responseArray[] = [
                "success"=>false,
                "id"=> $one_user["id"],
                "account_number"=> $one_user["account_number"],
                "message"=>$e->getMessage()
                ];
            }
            echo json_encode($responseArray);
            $con = null;
})($banks);


function generateID($record,$banks,$configs){
        $payload = [
                "type" => "nuban",
                "name" => $record['account_name'],
                "account_number" => $record['account_number'],
                "bank_code" => $banks[$record['bank_name']] ?? $banks['default'],
                "currency" => "NGN",
                "description" => $record['id']
        ];  

        $ch = curl_init(PAYSTACK_BASE_URL . '/transferrecipient');                                                                   
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");                                                                   
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));                                                                  
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);                                                                      
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(                                                                          
            'Content-Type: application/json',   
            'Authorization: Bearer '. trim($configs->PAYSTACK_API_KEY),
            'Content-Length: ' . strlen(json_encode($payload)))                                                                   
        );                                                                                                                                                                                                                                     
        $response = curl_exec($ch);
        curl_close($ch);
        return  json_decode($response);
}
?>
