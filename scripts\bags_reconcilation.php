<?php

require_once(__DIR__ . '/../connect_db.php');
require_once(__DIR__ . '/../constants.php');

$driver = new DBDriver;
$conn_finance = $driver->connectPgSql(PG_FINANCE_DB_NAME);
$conn_transport = $driver->connectPgSql(PG_TRANSPORTATION_DB_NAME);
$conn_inventory = $driver->connectPgSql(PG_INVENTORY_DB_NAME);

function fetchReceivingAndControllerRecords($conn_inventory)
{

    $query = "select a.transport_info_id, a.bags_received as bags, b.bags_received as controller_bags, b.total_cost, b.price_per_bag
    from harvest_receiving_record a join transport_controller b on a.transport_info_id = b.transport_info_id
    where a.bags_received <> b.bags_received";

    $stmt = $conn_inventory->prepare($query);
    $stmt->execute();
    $results = $stmt->fetchAll();
    print_r($results);

    foreach ($results as $row) {
        if ($row['bags'] == 0) {

            $updateQuery = "UPDATE transport_controller 
                            SET deactivate_flag = 1, comment = 'Deactivated due to 0 bags recorded on harvest receiving record' 
                            WHERE transport_info_id = ?";
            $updateStmt = $conn_inventory->prepare($updateQuery);
            $updateStmt->execute([$row['transport_info_id']]);
        } else {

            $receivedBags = $row['bags'];
            $totalCost = $receivedBags * $row['price_per_bag'];
            $controllerBags = $row['controller_bags']; 
            $newComment = "Updated from $controllerBags bags to $receivedBags bags based on harvest receiving record";

            $updateQuery = "UPDATE transport_controller 
                    SET bags_received = ?, total_cost = ?, 
                        comment = CONCAT(
                            CASE 
                                WHEN COALESCE(comment, '') = '' THEN ''
                                ELSE CONCAT(comment, ', ')
                            END, 
                            ?::text
                        ) 
                    WHERE transport_info_id = ?";
            $updateStmt = $conn_inventory->prepare($updateQuery);
            $updateStmt->execute([$receivedBags, $totalCost, $newComment, $row['transport_info_id']]);
        }
    }
}

fetchReceivingAndControllerRecords($conn_inventory);

?>
