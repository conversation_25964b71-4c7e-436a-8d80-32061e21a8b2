<?php

function export_bg_pay_tables() {
    try {
        $driver = new DBDriver;
        $conn_inv = $driver->connectPgSql(PG_INVENTORY_DB_NAME);

        //Fetch records from bg_pay_export table
        $sql = "SELECT id, name, classification, link FROM bg_pay_export";

        $stmt = $conn_inv->prepare($sql);
        $stmt->execute();

        $result = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if ($result) {
            return [
                'success' => true,
                'data' => $result,
                'message' => 'Records retrieved successfully',
            ];
        } else {
            return [
                'success' => false,
                'data' => [],
                'message' => 'No records found',
            ];
        }

    } catch (PDOException $e) {
        return [
            'success' => false,
            'data' => [],
            'message' => 'Error: ' . $e->getMessage(),
        ];
    }
}

?>