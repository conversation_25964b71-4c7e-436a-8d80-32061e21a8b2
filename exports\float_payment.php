<?php
require_once '../connect_db.php';
require_once '../constants.php';
require_once("../app/db/config/ConfigDbOperations.php");
require_once '../app/controllers/cors.php';
require_once '../app/controllers/payment_status.php';
require_once(__DIR__ . '/../app/models/exception.php');
require_once '../api/payment_helper.php';
require_once(__DIR__ . '/../api/auth.php');

cors();

validateUser();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    throw new CustomException(405, "Method Not Allowed. Only POST requests are allowed.");
}

$json = file_get_contents('php://input');
$data = json_decode($json, true);

$id = $data['id'] ?? null;

date_default_timezone_set('Africa/Lagos');
$dates = date("Y-m-d H:i:s");
$file = "float_payment";//File Name
$filename = $file . "-" . $dates;

$response = get_float_payment($id)["data"];

extract_payment_file($response, $filename);


?>