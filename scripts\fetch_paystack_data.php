<?php

ini_set('max_execution_time', '1000');

require_once(__DIR__ . "/../constants.php");
require_once(__DIR__ . "/../connect_db.php");
require_once(__DIR__ . "/../app/models/payment_secrets.php");
require_once(__DIR__ . "/../app/controllers/Logger.php");
require_once(__DIR__ . '/../app/models/exception.php');

$driver = new DBDriver;
$conn_finance = $driver->connectPgSql(PG_FINANCE_DB_NAME);

function insertIntoPaystackRecipientIdTable($conn_finance, $processed_results)
{
    $sql = " INSERT INTO paystack_recipient_id
    (recipient_id, name, description, account_number, account_name, bank_name, created_at, updated_at) 
    VALUES 
    (:recipient_id, :name, :description, :account_number, :account_name, :bank_name, :created_at, :updated_at)
    ON CONFLICT (recipient_id) DO NOTHING";

    $stmt = $conn_finance->prepare($sql);

    $current_date_time = date('Y-m-d H:i:s');

    foreach ($processed_results as $data) {
        $stmt->bindValue(':recipient_id', $data->recipient_code);
        $stmt->bindValue(':name', $data->name);
        $stmt->bindValue(':description', $data->description);
        $stmt->bindValue(':account_number', $data->details->account_number);
        $stmt->bindValue(':account_name', $data->details->account_name);
        $stmt->bindValue(':bank_name', $data->details->bank_name);
        $stmt->bindValue(':created_at', $current_date_time);
        $stmt->bindValue(':updated_at', $current_date_time);

        $stmt->execute();
    }
}

function getTotalCount($conn_finance)
{
        try {
                $configs = PaymentSecrets::selectMany($conn_finance);

                // Now make a request to Paystack Api     
                $page = 1;
                $perPage = 1;                                                                                            
                $ch = curl_init(PAYSTACK_BASE_URL . '/transferrecipient?page=' . $page . '&perPage=' .$perPage);
                curl_setopt($ch,  CURLOPT_ENCODING, "");
                curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
                curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                curl_setopt($ch,  CURLOPT_CUSTOMREQUEST, "GET");
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                        "Authorization: Bearer " . trim($configs->PAYSTACK_API_KEY),
                        "Cache-Control: no-cache",
                ));

          
                $response = curl_exec($ch);
                //print_r($response);

                if (curl_errno($ch)) {
                        // Log cURL error
                        error_log("cURL Error (" . curl_errno($ch) . "): " . curl_error($ch));
                        return setResponse(false, "Something went wrong", [], curl_errno($ch));
                }
                return json_decode($response);
        } catch (Exception $e) {
                return ["status" => false, "error" => $e->getMessage()];
        }
}

function getTotalRecipientId($conn_finance)
{
        $query = "SELECT COUNT(recipient_id) FROM paystack_recipient_id";

        $stmt = $conn_finance->prepare($query);
        $stmt->execute();
        $result = $stmt->fetchColumn(); //Return count of records.

        return (int)$result;
}

function getAllTransferRecipients($conn_finance)
{
        try {
                $configs = PaymentSecrets::selectMany($conn_finance);
                $total = getTotalCount($conn_finance);
                $pageCount = ceil($total->meta->total / PER_PAGE);
                $recordCount = getTotalRecipientId($conn_finance);
                $pageNum = intdiv($recordCount, PER_PAGE);
                $pageNum = ($pageNum == 0) ? 1 : $pageNum;

                for ($count=$pageNum; $count <= $pageCount; $count++) {
                        
                        // Now make a request to Paystack Api     
                        $page = $count;
                        $perPage = PER_PAGE;                                                                                            
                        $ch = curl_init(PAYSTACK_BASE_URL . '/transferrecipient?page=' . $page . '&perPage=' .$perPage);
                        curl_setopt($ch,  CURLOPT_ENCODING, "");
                        curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
                        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                        curl_setopt($ch,  CURLOPT_CUSTOMREQUEST, "GET");
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                                "Authorization: Bearer " . trim($configs->PAYSTACK_API_KEY),
                                "Cache-Control: no-cache",
                        ));
        
                  
                        $response = curl_exec($ch);
        
                        if (curl_errno($ch)) {
                                // Log cURL error
                                error_log("cURL Error (" . curl_errno($ch) . "): " . curl_error($ch));
                                return setResponse(false, "Something went wrong", [], curl_errno($ch));
                        }
                    
                        $result = json_decode($response);
                        $processed_results = $result->data;
        
                        insertIntoPaystackRecipientIdTable($conn_finance, $processed_results);
                }
                return $count;

        } catch (Exception $e) {
                return ["status" => false, "error" => $e->getMessage()];
        }
}

function processing_data($conn_finance)
{
    try {
        $start = microtime(true);
        echo  date('Y-m-d H:i:s.u') . "  🎶🎶🎶  =====FETCHING PAYSTACK RECIPIENT ID CRON ACTIVATED, READY FOR TAKEOFF==== 🎶🎶🎶 <br /> \n";
        $logArray = [];

        echo  date('Y-m-d H:i:s.u') . " ✋✋......Now fetching Recipient ID from Paystack and inserting into paystack recipient id table... ✋✋ <br /> \n";
        getAllTransferRecipients($conn_finance);
        echo  date('Y-m-d H:i:s.u') . " ✔✔✔✔ ......Done inserting records into paystack recipient id table...✔✔✔✔ <br /> \n";

        $end = microtime(true);
        $elapsed = $end - $start;
        $logArray[] = getLogString("FLOAT CRON",  "🎵🎵🎵 Bravo!!!! CRON ran successfully in " . $elapsed . " seconds 🎵🎵🎵");
        $logArray[] = getLogString("FLOAT CRON", '🔊🔊🔊🔊🔊 - JOB WELL DONE - 🔊🔊🔊🔊🔊');
        $logArray[] = getLogString("FLOAT CRON",  "👏👏👏 PAYSTACK RECIPIENT ID CRON TOUCH DOWN SUCCESSFULLY...👏👏👏👏");

        foreach ($logArray as $log) {
            echo $log;
        }
    } catch (Exception $e) {
        error_log('Initiate Processing Error: ' . $e->getMessage());
        echo " 🙈🙈🙈...Something went wrong. Float cron failed: 🙈🙈🙈 " . $e->getMessage();
    }
}

processing_data($conn_finance);

?>