<?php
require_once (__DIR__.'/../../constants.php');
require_once (__DIR__.'/../../connect_db.php');
require_once (__DIR__.'/../../app/controllers/__payment_validations.php');
require_once (__DIR__.'/../auth.php');
require_once (__DIR__.'/../../app/models/exception.php');
require_once (__DIR__.'/../../app/models/payment_queue.php');
require_once (__DIR__.'/../../app/models/harvest_tg_payments.php');
require_once (__DIR__.'/../../app/models/app_logs.php');
require_once (__DIR__.'/../payment_helper.php');
require_once '../../app/controllers/cors.php';

ini_set('max_execution_time', '300');

cors();

(function(){
    try{
        $user = validateUser();
        
        $driver = new DBDriver;
        $conn = $driver->connectPgSql(PG_FINANCE_DB_NAME);
        $conn_inventory = $driver->connectPgSql(PG_INVENTORY_DB_NAME);


        if($_SERVER['REQUEST_METHOD'] != 'POST') throw new CustomException(403,"Request Method Not Allowed");
        $json = file_get_contents('php://input');

        $data = json_decode($json, true);

        if(!isValidBulkPayload($data)) throw new CustomException(400,"Invalid bulk payload, one or more parameters are missing or invalid");

        
        $initiator_name = $user->data->staff_name;
        $initiator_role = $user->data->config[0]->permission_name;


        $responseArray = [];

        foreach($data['data'] as $one_pay){
            $errors = validatePaymentPayload($one_pay);

            if (!empty($errors)) {
                $responseArray[] = [
                    "success" => false,
                    "key" => json_encode($one_pay),
                    "errors" => $errors,
                ];
            }else{
                 try{
                    $one_pay["reference"] = generateReference($one_pay["payee_id"]);
                    if($one_pay['payment_type'] == 'harvest_advance'){
                        $tg_info = TGPayments::selectCustom($conn, ['payment_ready'], ["t.ik_number"=> $one_pay['payee_id']]);
                        $payout = payout($conn_inventory);
                        if(!$tg_info) throw new CustomException(400,"Invalid payment payload, this TG {$one_pay['payee_id']} Card has received payment or payee id is not valid");
                        if($tg_info[0]['amount_payable'] == 0) throw new CustomException(400,"Invalid payment payload, this TG {$one_pay['payee_id']} has been paid exact amount before");
                        if ($tg_info[0]['net_harvest_advance'] > $tg_info[0]['id_loan_size'] * $payout[0]['value'] || $tg_info[0]['net_harvest_advance'] < $tg_info[0]['id_loan_size'] * $payout[1]['value']) {
                            throw new CustomException(400,"Invalid payment payload, this TG {$one_pay['payee_id']} exceeded harvest advance price per hectare");
                        }
                    }
                    // Check for Frozen payment
                    if($one_pay['payment_type'] == 'float_payment'){
                        $isPayeeFrozen = getFloatFreezeStatus($conn, $one_pay['payee_id']);
                        if($isPayeeFrozen['freeze_flag'] == 1) throw new CustomException(400,"This personnel is frozen and cannot receive payments.");
                    }
                    PaymentQueue::insertFloatOne($conn, $conn_inventory, $one_pay, $initiator_name, $initiator_role); 
                    $responseArray[] = [
                        "success"=>true,
                        "reference_id" => $one_pay["reference"],
                         "payee_id"=> $one_pay["payee_id"],
                          "message"=>"Payment successfully queued"
                         ];
                 }
                 catch(Exception $err){
                    $responseArray[] = [
                        "success"=>false,
                         "payee_id"=> $one_pay["payee_id"],
                          "message"=> "Not queued...".$err->getMessage()
                         ];
                 }
               }
        }
        http_response_code(201);
        echo json_encode(setResponse(true,"Bulk payment request received ",$responseArray, null));
        AppLogs::insertOne($conn,generateLogs($user->data->staff_id,"SUBMITTED BULK PAYMENT", $data));
    }
    catch(CustomException $e){
        http_response_code($e->status ?? 500);
        echo json_encode(setResponse(false,"Something went wrong... Payments not queued ",[], $e->getMessage()));
    }
})()

?>