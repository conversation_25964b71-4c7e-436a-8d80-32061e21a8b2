<?php

class CheckingRecords
{
    public static function getAllRecords($con)
    {
        try {
            $stmt = $con->prepare("SELECT * FROM harvest_checking_record");
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $err) {
            throw new Exception("Ooops!!! Looks like we couldn't fetch all checking records - " . $err->getMessage());
        }
    }


    public static function getFlaggedRecords($con)
    {
        try {
            $stmt = $con->prepare("SELECT * FROM harvest_checking_record WHERE verfifier_flag = 1");
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $err) {
            throw new Exception("Ooops!!! Looks like we couldn't fetch all flagged checking records - " . $err->getMessage());
        }
    }

    /* getDualRecords : fetches all records on checking and clearing officer records but not on cleared records
         * @param $con : database connection object
         * returns - an associatic=ve array of hsf records
         */
    // public static function getDualRecords($con)
    // {
    //     try {
    //         // $hsf_columns =
    //         //     'a.hsf_id, a.hub, a.unique_member_id,a.ik_number, a.empty_bag_weight,a.product_type,a.variety,a.verifier_flag,a.moldy_grains_count, a.collection_center_id,
    //         //  a.total_weight,a.individual_weight,a.bags_marketed,a.moisture_percentage,a.cleanliness_percentage, b.unique_member_id, 
    //         // b.total_weight,b.individual_weight, b.empty_bag_weight,b.bags_marketed,b.comment, b.verifier_flag, b.moisture_percentage,
    //         // b.cleanliness_percentage,b.moldy_grains_count,c.transportation_flag,c.threshing_flag';

    //         $hsf_columns =
    //             'a.hsf_id, a.unique_member_id,a.ik_number, a.empty_bag_weight,a.product_type,a.variety,a.verifier_flag,a.moldy_grains_count, a.collection_center_id,
    //          a.total_weight,a.individual_weight,a.bags_marketed,a.moisture_percentage,a.cleanliness_percentage, b.unique_member_id, b.hub_id, b.hub_name,
    //         b.total_weight,a.average_weight,b.average_weight, b.empty_bag_weight,b.bags_marketed,b.comment, b.verifier_flag, b.moisture_percentage,
    //         b.cleanliness_percentage,b.moldy_grains_count,b.imei,b.app_version,c.transporter_id';

    //         // $query = "SELECT {$hsf_columns} FROM harvest_scaling_record a 
    //         //           JOIN harvest_checking_record b USING(hsf_id) JOIN harvest_receiving_record c USING (hsf_id)
    //         //           WHERE a.hsf_id NOT IN (SELECT hsf_id FROM harvest_cleared_record)";

    //         $query = "SELECT {$hsf_columns} FROM harvest_scaling_record a 
    //                   JOIN harvest_checking_record b USING(hsf_id) JOIN harvest_receiving_record c USING (hsf_id)
    //                   WHERE a.hsf_id NOT IN (SELECT hsf_id FROM harvest_cleared_record)";

    //         $stmt = $con->prepare($query);
    //         $stmt->execute();
    //         return $stmt->fetchAll(PDO::FETCH_NAMED);
    //     } catch (Exception $err) {
    //         throw new Exception("Ooops!!! Looks like we couldn't fetch all pending dual records - " . $err->getMessage());
    //     }
    // }

    public static function getDualRecords($con)
    {
        try {
            $scaling_columns = '
            a.scaling_waybill_id, a.unique_member_id, a.ik_number, a.total_weight, a.bags_marketed, 
            a.empty_bag_weight AS empty_bag_weight, a.product_type, a.variety, a.verifier_flag, a.moldy_grains_count, a.average_weight, 
            a.collection_center_id, a.moisture_percentage, a.cleanliness_percentage, a.hub_id, a.hub_name, a.average_weight, a.imei, a.app_version,
            b.unique_member_id AS checkerScaler_member_id, b.bags_marketed AS checkerScaler_bags_marketed,
            b.total_weight AS checkerScaler_total_weight, b.empty_bag_weight AS checkerScaler_empty_bag_weight';

            $receiving_columns = 'c.receiving_waybill_id, c.unique_member_id AS receiver_unique_member_id, 
            c.bags_received AS receiver_bags_received, c.transportation_flag AS receiver_transportation_flag,
            c.threshing_flag, c.threshing_field_id, c.verifier_flag AS receiver_verifier_flag,
            d.unique_member_id AS checkerreceiver_member_id, d.transportation_flag AS checkerreceiver_transportation_flag, 
            d.bags_received AS checkerreceiver_bags_received, d.transporter_id';

            $query = "SELECT {$scaling_columns}, {$receiving_columns}
          FROM harvest_scaling_record a
          JOIN harvest_checker_scaler_record b ON a.scaling_waybill_id = b.scaling_waybill_id
          JOIN harvest_receiving_record c ON a.scaling_waybill_id = c.scaling_waybill_id
          JOIN harvest_checker_receiver_record d ON c.receiving_waybill_id = d.receiving_waybill_id
          WHERE a.scaling_waybill_id NOT IN (SELECT waybill_id FROM harvest_cleared_record)";



            $stmt = $con->prepare($query);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_NAMED);
        } catch (Exception $err) {
            throw new Exception("Ooops!!! Looks like we couldn't fetch all pending dual records - " . $err->getMessage());
        }
    }



    public static function getReceivingRecords($con)
    {
        try {
            $receiving_columns = 'c.receiving_waybill_id, c.unique_member_id AS receiver_unique_member_id, 
            c.bags_received AS receiver_bags_received, c.transportation_flag AS receiver_transportation_flag,
            c.threshing_flag, c.threshing_field_id, c.scaling_waybill_id, c.verifier_flag AS receiver_verifier_flag,
            d.unique_member_id AS checkerreceiver_member_id, d.transportation_flag AS checkerreceiver_transportation_flag, 
            d.bags_received AS checkerreceiver_bags_received, d.transporter_id';

            $query = "SELECT {$receiving_columns}
          FROM harvest_receiving_record c 
          JOIN harvest_checker_receiver_record d ON c.receiving_waybill_id = d.receiving_waybill_id
          WHERE c.scaling_waybill_id NOT IN (SELECT waybill_id FROM harvest_cleared_record)";

            $stmt = $con->prepare($query);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_NAMED);
        } catch (Exception $err) {
            throw new Exception("Ooops!!! Looks like we couldn't fetch all pending dual records - " . $err->getMessage());
        }
    }

    public static function getScalingRecords($con)
    {
        try {
            $scaling_columns = '
            a.scaling_waybill_id, a.unique_member_id, a.ik_number, a.total_weight, a.bags_marketed, a.receiving_waybill_ids, 
            a.empty_bag_weight AS empty_bag_weight, a.product_type, a.variety, a.verifier_flag, a.moldy_grains_count, a.average_weight, 
            a.collection_center_id, a.moisture_percentage, a.cleanliness_percentage, a.hub_id, a.hub_name, a.imei, a.app_version,
            b.unique_member_id AS checkerScaler_member_id, b.bags_marketed AS checkerScaler_bags_marketed,
            b.total_weight AS checkerScaler_total_weight, b.empty_bag_weight AS checkerScaler_empty_bag_weight,
            b.average_weight AS checkerScaler_average_weight,
            b.moldy_grains_count as checkerScaler_moldy_grain_count, b.moisture_percentage as checkerScaler_moisture_percentage,
            b.cleanliness_percentage as checkerScaler_cleanliness_percentage';


            $query = "SELECT {$scaling_columns}
          FROM harvest_scaling_record a
          JOIN harvest_checker_scaler_record b ON a.scaling_waybill_id = b.scaling_waybill_id
          WHERE a.scaling_waybill_id NOT IN (SELECT waybill_id FROM harvest_cleared_record)";

            $stmt = $con->prepare($query);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_NAMED);
        } catch (Exception $err) {
            throw new Exception("Ooops!!! Looks like we couldn't fetch all pending dual records - " . $err->getMessage());
        }
    }


    /* updateMany : updates specified columns for many records on the checking records table
         * @param $con : database connection object
         * @param $hsf_array : an array of HSF ids
         * @param $update_pairs : an associative array of column names and values to be updated to
         * returns - null
         */
    public static function updateMany($con, $hsf_array, $update_pairs)
    {
        $hsf_ids = array_column($hsf_array, 'hsf_id');
        $update_string = '';

        try {
            // build up the update string
            foreach ($update_pairs as $key => $value) {
                $update_string .= "{$key}='{$value}',";
            }
            $chunks = array_chunk($hsf_ids, 65000);
            foreach ($chunks as $eachChunk) {
                $placeholder_string = join(",", array_fill(0, count($eachChunk), "?"));
                $query = "UPDATE harvest_checking_record SET ${update_string} updated_at = NOW() WHERE hsf_id IN ({$placeholder_string})";
                $stmt = $con->prepare($query);
                $stmt->execute(array_values($eachChunk));
            }
        } catch (Exception $err) {
            throw new Exception("Ooops!!! Looks like we couldn't update all checker flags - " . $err->getMessage());
        }
    }
}
