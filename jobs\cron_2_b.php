<?php
require_once(__DIR__ . "/../app/models/harvest_member_payment_update.php");
require_once(__DIR__ . "/../app/models/clearance_members.php");
require_once(__DIR__ . "/../app/controllers/Logger.php");
require_once(__DIR__ . "/../app/controllers/_group_by.php");
require_once(__DIR__ . "/../app/models/cc_farm_loan.php");
require_once(__DIR__ . "/../app/controllers/__fetch_sum.php");
require_once(__DIR__ . "/../app/controllers/__array_key_concat.php");
require_once(__DIR__ . "/../app/models/grain_hub_price.php");
require_once(__DIR__ . "/../app/controllers/_get_or_default.php");
require_once(__DIR__ . "/../connect_db.php");
require_once(__DIR__ . "/../constants.php");
require_once(__DIR__ . "/../app/db/config/ConfigDbOperations.php");


// Establish DB Connections
$driver = new DBDriver;
$conn_inventory = $driver->connectPgSql(PG_INVENTORY_DB_NAME);
$conn_finance = $driver->connectPgSql(PG_FINANCE_DB_NAME);
date_default_timezone_set('Africa/Lagos');
echo "Version: v0.0.10 \n";


// Fetch Config Values from the Database
$configDbOperations = new ConfigDbOperations($conn_inventory);
$configModel = $configDbOperations->get_data();

$cron2Job = new Cron2b($conn_finance);
$cron2logs = $cron2Job->startCron($configModel);
foreach ($cron2logs as $log) {
    echo $log;
}




class Cron2b
{
    private HarvestMemberPaymentUpdate $harvestMemberPaymentUpdate;

    private GrainHubPrice $grainHubPrice;


    private array $logs;
    private CcFarmLoan $ccFarmLoan;

    function __construct($trans_pg_con)
    {
        $this->harvestMemberPaymentUpdate = new HarvestMemberPaymentUpdate($trans_pg_con);
        $this->grainHubPrice = new GrainHubPrice($trans_pg_con);
        $this->ccFarmLoan = new CcFarmLoan($trans_pg_con);
        $this->logs = array();
    }

    function startCron($config)
    {
        $startTime = microtime(true);
        echo date('Y-m-d H:i:s.u') . "=====Cron 2b Started==== <br /> \n";

        try {
            $this->initialDataComputation();
            $this->logs[] = $this->getLogString("Fetching Data from harvest_members_payment table");
            $harvest_members = $this->harvestMemberPaymentUpdate->get_data($config->season_config);
            $this->logs[] = $this->getLogString(count($harvest_members) . " data fetched successfully");


            $this->logs[] = $this->getLogString("Fetching Grain Prices from grain_price_hub table");
            $grain_prices = $this->grainHubPrice->get_data();
            $this->logs[] = $this->getLogString(count($grain_prices) . " data fetched successfully");

            $this->logs[] = $this->getLogString("Fetching Data from cc_farm_loan table");
            $cc_farm_loan = $this->ccFarmLoan->get_data($harvest_members);
            $this->logs[] = $this->getLogString(count($cc_farm_loan) . " data fetched successfully");

            $members_to_update = array();

            foreach ($harvest_members as $cc_payment_member) {
                $unique_member_id = $cc_payment_member["unique_member_id"];
                $product = $cc_payment_member["product"];
                $key = get_key_concat($unique_member_id, $product);
                $this->logs[] = $this->getLogString("Computing Data for Unique Member ID {$unique_member_id} and Product Type {$product}");

                $hub_name = $cc_payment_member["hub_name"];

                $cc_payment_member["updated_flag"] = 1;
                $net_weight = $cc_payment_member["net_weight_marketed"];
                $costs =  $cc_payment_member["threshing_cost"] + $cc_payment_member["transport_cost"] + $cc_payment_member["processing_cost"];
                $cc_payment_member["total_cost"] = $costs;
                $cc_payment_member["misc_account"] = get_or_default($cc_farm_loan, get_key_concat($unique_member_id, $product), array())["misc_account"] ?? 0;
                $cc_payment_member["loan_before_harvest"] = get_or_default($cc_farm_loan, get_key_concat($unique_member_id, $product), array())["loan_before_harvest"] ?? 0;

                $this->logs[] = $this->getLogString("Total computed data:");

                $this->logs[] = $this->getLogString("Misc Amount: {$cc_payment_member["misc_account"]}");
                $this->logs[] = $this->getLogString("Loan before harvest: {$cc_payment_member["loan_before_harvest"]}");

                $contractual_threshold = $cc_payment_member["updated_no_of_bags_marketed"] / $cc_payment_member["field_size"];

                if ($contractual_threshold < $config->contractual_threshold_config) {
                    $cc_payment_member["contractual_flag"] = 1;
                    $cc_payment_member["exceeded_expectation"] = 0;

                } else if ($contractual_threshold > $config->contractual_threshold_config) {
                    $cc_payment_member["exceeded_expectation"] = 1;
                    $cc_payment_member["contractual_flag"] = 0;

                } else {
                    $cc_payment_member["contractual_flag"] = 0;
                    $cc_payment_member["exceeded_expectation"] = 0;
                }

                if (isset($grain_prices[get_key_concat($product, $hub_name)])) {
                    $price_per_kg = $grain_prices[get_key_concat($product, $hub_name)]["price"];
                    $value_of_grain = $price_per_kg * $net_weight;

                    $cc_payment_member["grain_value"] = $value_of_grain;

                    $this->logs[] = $this->getLogString("Grain price per kg: {$price_per_kg}");

                    $this->logs[] = $this->getLogString("Value of grain: {$value_of_grain}");

                    $harvest_advance = $value_of_grain - ($cc_payment_member["loan_before_harvest"] + $costs) + $cc_payment_member["misc_account"];

                    $cc_payment_member["harvest_advance"] = $harvest_advance;

                    $members_to_update[] = $cc_payment_member;
                } else {
                    $this->logs[] = $this->getLogString("Grain Price not found for Grain: {$product} and Hub: {$hub_name}");
                }
            }

            $this->logs[] = $this->getLogString("Updating " . count($members_to_update) . " data into harvest members payment update");

            $this->harvestMemberPaymentUpdate->insert_many($members_to_update, 2000);

            $this->logs[] = $this->getLogString(count($members_to_update) . " data has been updated in harvest members payment update");

            $ik_numbers = array_map(array($this, "getIkNumber"), $members_to_update);

            if (count($ik_numbers) > 0) {
                $harvest_iks = $this->harvestMemberPaymentUpdate->get_harvest_advance($ik_numbers);

                $grouped_data = $this->group_harvest_advance($harvest_iks);

                $this->logs[] = $this->getLogString("Computing harvest advance for updated data");

                $harvest_advance_update = array();
                foreach ($members_to_update as $member) {
                    $key = $member["ik_number"];
                    $harvest_advance = $member["harvest_advance"];
                    if ($harvest_advance > 0) {
                        $percentage_ownership = round($harvest_advance / $grouped_data[$key]["positive"], 9);
                        $shared_debt = round($percentage_ownership * $grouped_data[$key]["negative"], 2);
                        $net_harvest_advance = round($harvest_advance + $shared_debt, 2);
                        $member["percentage_ownership"] = $percentage_ownership;
                        $member["shared_debt"] = $shared_debt;
                        $member["net_harvest_advance"] = $net_harvest_advance;
                        $this->logs[] = $this->getLogString($member["unique_member_id"] . " Percentage Ownership: {$percentage_ownership}");
                        $this->logs[] = $this->getLogString($member["unique_member_id"] . " Shared Debt: {$shared_debt}");
                        $this->logs[] = $this->getLogString($member["unique_member_id"] . " Net Harvest Advance: {$net_harvest_advance}");
                    } else {
                        $member["shared_debt"] = 0;
                        $member["net_harvest_advance"] = 0;
                        $member["percentage_ownership"] = 0;
                        $this->logs[] = $this->getLogString($member["unique_member_id"] . " has a negative harvest advance");
                    }
                    $harvest_advance_update[] = $member;

                }

                $this->logs[] = $this->getLogString("Updating " . count($harvest_advance_update) . " data into harvest members payment");

                $this->harvestMemberPaymentUpdate->insert_many($harvest_advance_update, 2000);

                $this->logs[] = $this->getLogString(count($harvest_advance_update) . " data has been updated in harvest members payment");
            }


            $endTime = microtime(true);

            $timeElapsed = ($endTime - $startTime);
            $this->logs[] = $this->getLogString("Computation ran in {$timeElapsed}s");
        } catch (Exception $exception) {
            $this->logs[] = $this->getLogString("Error occurred: {$exception->getMessage()}");
        }

        $endTime = microtime(true);

        $timeElapsed = ($endTime - $startTime);
        $this->logs[] = $this->getLogString("Job ran in {$timeElapsed}s");

        return $this->logs;
    }

    private function getLogString($message): string
    {
        return getLogString("CRON 2", $message);
    }

    private function getIkNumber($record)
    {
        $ik_number = $record["ik_number"];
        return "'$ik_number'";
    }

    private function group_harvest_advance(array $harvest_iks): array
    {
        $grouped = array();

        foreach ($harvest_iks as $member) {
            $key = $member["ik_number"];
            if (!isset($grouped[$key])) {
                $grouped[$key]["positive"] = 0;
                $grouped[$key]["negative"] = 0;
            }
            $harvest_advance = $member["harvest_advance"];
            if ($harvest_advance < 0) {
                $grouped[$key]["negative"] += $harvest_advance;
            } else {
                $grouped[$key]["positive"] += $harvest_advance;
            }
        }

        return $grouped;
    }

    private function initialDataComputation()
    {
        $this->harvestMemberPaymentUpdate->reset_update_flag();
    }
}