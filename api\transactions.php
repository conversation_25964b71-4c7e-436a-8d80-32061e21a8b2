<?php

require_once '../connect_db.php';
require_once '../constants.php';
require_once '../app/controllers/cors.php';
require_once(__DIR__ . '/../app/models/exception.php');
require_once(__DIR__ . '/../app/models/harvest_transactions.php');
require_once('auth.php');

cors();

//$user = validateUser();

$driver = new DBDriver;
$conn = $driver->connectPgSql(PG_FINANCE_DB_NAME);

// $response = HarvestTransactions::selectMany($conn);
$response = HarvestTransactions::getHarvestTransactions($conn);

http_response_code(200);
echo json_encode(setResponse(true,"Harvest transactions fetched successfully ",$response, null));

?>
