<?php
require_once (__DIR__.'/../constants.php');
require_once (__DIR__.'/../scripts/pay_recipient.php');
require_once (__DIR__.'/../app/controllers/__payment_validations.php');
require_once (__DIR__.'/../app/models/exception.php');
require_once ('auth.php');

header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json');


cors();

$user = validateUser();

(function(){
    try{
        if($_SERVER['REQUEST_METHOD'] != 'POST') throw new CustomException(403,"Request Method Not Allowed",[]);
        $json = file_get_contents('php://input');
        if(empty($json)) throw new CustomException(400,"One or more parameters are missing or invalid",[]);
        $data = json_decode($json, true);
        
        if(!isValidPaymentPayload($data)) throw new CustomException(400,"One or more parameters are missing or invalid",[]);

        $payment_response = payOne($data);
        
        // if(!$payment_response['success']){
        //     http_response_code(500);
        //     echo json_encode($payment_response);
        //     exit;    
        // }
        http_response_code(200);
        echo json_encode($payment_response);
    }
    catch(Exception $e){
        http_response_code($e->status ?? 500);
        echo json_encode(setResponse(false,"Something went wrong.. ".$e->getMessage(),[]));  
    }
})()


?>