<?php
require_once (__DIR__.'/../constants.php');
require_once (__DIR__.'/../connect_db.php');
require_once (__DIR__.'/../app/controllers/__payment_validations.php');
require_once (__DIR__.'/../app/controllers/__response_helpers.php');
require_once (__DIR__.'/../app/models/exception.php');
require_once (__DIR__.'/../app/models/harvest_tg_payments.php');
require_once (__DIR__.'/../app/models/payment_queue.php');
require_once (__DIR__.'/../app/controllers/cors.php');
require_once (__DIR__.'/../app/models/app_logs.php');


ini_set('max_execution_time', '300');

if(isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] == 'GET') {
$user = validateUser();
if(!$user){
    http_response_code(401);
    echo(json_encode(setResponse(false,"Something went wrong", [], "Invalid request. User could not be authenticated")));
    exit;
}
}


if(isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] != 'GET') {
    http_response_code(403);
    echo(json_encode(setResponse(false,"Something went wrong", [], "Request Method Not Allowed")));
    exit;
}
      (function (){
        $driver = new DBDriver;
        $conn = $driver->connectPgSql(PG_FINANCE_DB_NAME);
        $groups = TGPayments::selectNoRiskGroups($conn);
        $responseArray = [];
        foreach($groups as $group){
            $group['payment_type'] = 'harvest_advance';
            $group['reason'] = "HA paid to {$group['payee_id']} by BABBAN GONA FARMS on ".date('Y-m-d H:i:s');
               if(!isValidPaymentPayload($group)){
               $responseArray[] = [
                "success"=>false,
                 "key"=> json_encode($group),
                  "message"=>"Invalid parameters, not queued"
                 ];
                }
               else{
                 try{
                    $group["reference"] = generateReference($group["payee_id"]);
                    // AM I GENERATING REFERENCE AT THIS STAGE. CONFIRM
                    PaymentQueue::insertOne($conn, $group); 
                    $responseArray[] = [
                        "success"=>true,
                         "payee_id"=> $group["payee_id"],
                         "amount"=> $group["amount"],
                         "reference"=> $group["reference"],
                          "message"=>"Payment successfully queued"
                         ];
                 }
                 catch(Exception $err){
                    $responseArray[] = [
                        "success"=>false,
                         "payee_id"=> $group["payee_id"],
                         "amount"=> $group["amount"],
                         "reference"=> $group["reference"],
                          "message"=>"Not queued...".$err->getMessage()
                         ];
                 }
               }
        }
        echo json_encode($responseArray);
    })()

?>