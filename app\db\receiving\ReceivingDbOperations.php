<?php

require_once(__DIR__ . "/../../models/ReceivingRecord.php");

class ReceivingDbOperations
{

    private ?PDO $connection;

    function __construct($connection)
    {
        $this->connection = $connection;
    }

    public function get_data(): array
    {
        $query = "select receiving_waybill_id, unique_member_id, transportation_flag, threshing_flag, transaction_date, threshing_field_id, scaling_waybill_id, transport_info_id, bags_received from public.harvest_receiving_record";

        $stmt = $this->connection->prepare($query);

        $stmt->execute();

        $result = array();
        while ($data = $stmt->fetchObject('ReceivingRecord')) {
            $result[$data->getScalingWaybillId()] = $data;
        }
        return $result;
    }
}