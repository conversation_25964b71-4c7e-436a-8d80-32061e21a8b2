<?php
require_once(__DIR__ . "/../controllers/__data_helpers.php");

class FloatCards{

    public static function selectPending($con){
        try{
            $query = "SELECT * FROM float_payment_config WHERE (recipient_id IS NULL OR NOT recipient_id ILIKE '%RCP%')
                      AND LENGTH(account_number) = 10  AND profiling_attempts < 2 LIMIT 100";
            $stmt = $con->prepare($query);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $err) {
            throw new Exception("Ooops!!! Looks like we couldn't fetch all float records - " . $err->getMessage());
        }
    }

    public static function updateOne($con, $id, $update_pairs)
    {
        $update_string = '';
        try {
            // build up the update string
            $index = 0;
            foreach ($update_pairs as $key => $value) {
                $update_string .= "{$key} ='{$value}'";
                if($index < count($update_pairs) - 1 )
                $update_string .= ",";
                ++$index;
            }
            $stmt = $con->prepare("UPDATE float_payment_config SET ${update_string} WHERE id = ?");
            $stmt->execute([$id]);
        } catch (Exception $err) {
            throw new Exception("Ooops!!! Looks like we couldn't update all float records - " . $err->getMessage());
        }
    }
}