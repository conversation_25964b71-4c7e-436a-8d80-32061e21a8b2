<?php
require_once(__DIR__ . '/../auth.php');
require_once '../../app/controllers/cors.php';
require_once(__DIR__ . '/../../constants.php');
require_once(__DIR__ . '/../../connect_db.php');
require_once(__DIR__ . '/../../app/models/app_logs.php');

cors();

$user = validateUser();

$driver = new DBDriver;
$conn = $driver->connectPgSql(PG_FINANCE_DB_NAME);

function downloadHATemplate()
{
    try {
        $user = validateUser();

        $driver = new DBDriver;
        $conn = $driver->connectPgSql(PG_FINANCE_DB_NAME);

        $columns = ['hub', 'grain', 'price'];

        header('Content-Type: text/csv');
        header('Content-Disposition: attachment;filename="harvest_advance_config_template.csv"');

        $fp = fopen('php://output', 'w');
        fputcsv($fp, $columns);
        fclose($fp);

        // Logging the user's action
        AppLogs::insertOne($conn, generateLogs($user->data->staff_id, "USER DOWNLOADED HARVEST ADVANCE CONFIG TEMPLATE", ""));
    } catch (CustomException $e) {
        http_response_code($e->status ?? 500);
        echo json_encode(['status' => false, 'message' => $e->getMessage()]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['status' => false, 'message' => $e->getMessage()]);
    }
}

downloadHATemplate();

?>
