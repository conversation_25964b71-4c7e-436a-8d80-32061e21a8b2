<?php
require_once(__DIR__ . "/../app/models/harvest_member_payment.php");
require_once(__DIR__ . "/../app/models/harvest_member_payment_soy.php");
require_once(__DIR__ . "/../app/models/clearance_members.php");
require_once(__DIR__ . "/../app/controllers/Logger.php");
require_once(__DIR__ . "/../app/controllers/_group_by.php");
require_once(__DIR__ . "/../app/db/cleared/ClearedDbOperations.php");
require_once(__DIR__ . "/../app/models/cc_farm_loan.php");
require_once(__DIR__ . "/../app/controllers/__fetch_sum.php");
require_once(__DIR__ . "/../app/controllers/__array_key_concat.php");
require_once(__DIR__ . "/../app/models/grain_hub_price.php");
require_once(__DIR__ . "/../app/controllers/_get_or_default.php");


/**
 * The Cron_2 handles transactions on the harvest_member_payment, it computes the grain value ( for this it retrieves the data on harvest advance from the hub_grain_price table), 
 * it pulls in the member loan value and the member miscellaneous value from the cc_farm_loan table. Note that the system completes all these analysis in memory and upon completing all
 * computation, it updates the relevant columns in the harvest member payment table.
 * 
 * This Cron also helps with computing certain flags on the table. there are 2 flags to note at this point
 * Contractual flag -  this is simply set to 1 when a member does not bring 20bags per hectare
 * Exceed expectation -  this is simply when a member brings more than 20bags per hectare
 * 
 * A final computation this system runs is to compute the percentage ownership, shared debt in the event that a member fails contractual and he as not 
 * marketed enough bags to clear his loan.
 * 
 */

class Cron2
{
    private HarvestMemberPayment $harvestMemberPayment;
    private HarvestMemberPaymentSoy $harvestMemberPaymentSoy;

    private ClearedDbOperations $clearedDbOperations;
    private GrainHubPrice $grainHubPrice;


    private array $logs;
    private CcFarmLoan $ccFarmLoan;

    function __construct($trans_pg_con, $pg_con, $conn_transport)
    {
        $this->harvestMemberPayment = new HarvestMemberPayment($trans_pg_con);
        $this->clearedDbOperations = new ClearedDbOperations($pg_con);
        $this->grainHubPrice = new GrainHubPrice($trans_pg_con);
        $this->ccFarmLoan = new CcFarmLoan($trans_pg_con);
        $this->harvestMemberPaymentSoy = new HarvestMemberPaymentSoy($trans_pg_con);
        $this->logs = array();
    }

    function startCron($config, $conn_farming, $conn_inventory, $conn_transport)
    {
        $startTime = microtime(true);
        echo date('Y-m-d H:i:s.u') . "=====Cron 2 Started==== <br /> \n";

        try {
            $this->harvestMemberPayment->reset_cash_local_update_flag();
            $this->harvestMemberPayment->reset_cash_local_data();
            $cshData = $this->clearedDbOperations->selectCashLocal();


            $members_for_insert = [];

            foreach ($cshData as $eachMember) {
                $one_member = [];

                $one_member['unique_member_id'] = $eachMember["unique_member_id"];
                $one_member['ik_number'] = $eachMember["ik_number"];
                $one_member['product'] = 'Cash Local';
                $one_member['season'] = $config->season_config;
                $one_member['field_size'] = 0;
                $one_member['member_status'] = 1;
                $one_member['percentage_ownership'] = null;
                $one_member['no_of_bags_marketed'] = $eachMember['bags_marketed'];
                $one_member['net_weight_marketed'] = $eachMember['net_weight'];
                $one_member['grain_value'] = null;
                $one_member['harvest_advance'] = null;
                $one_member['loan_before_harvest'] = null;
                $one_member['net_harvest_advance'] = null;
                $one_member['misc_account'] = 0;
                $one_member['threshing_cost'] = $eachMember["threshing_cost"];
                $one_member['transport_cost'] = $eachMember["transport_cost"];
                $one_member['processing_cost'] = $eachMember["processing_cost"];
                $one_member['total_cost'] = $eachMember["total_cost"];
                $one_member['shared_debt'] = null;
                $one_member['payment_ready_date'] = null;
                $one_member['updated_flag'] = 0;
                $one_member['contractual_flag'] = 0;
                $one_member['exceeded_expectation'] = 0;

                $members_for_insert[] = $one_member;
            }
            //print_r($members_for_insert);
            if (count($members_for_insert) > 0)
                $this->harvestMemberPayment->insert_many($members_for_insert, 2000);

            $this->initialDataComputation();
            $this->logs[] = $this->getLogString("Fetching Data from harvest_members_payment table");
            $harvest_members = $this->harvestMemberPayment->get_data($config->season_config);
            $this->logs[] = $this->getLogString(count($harvest_members) . " data fetched successfully");

            $this->logs[] = $this->getLogString("Fetching Data from cleared records table");
            $cleared_data = $this->clearedDbOperations->get_data_all($harvest_members);
            $this->logs[] = $this->getLogString(count($cleared_data) . " data fetched successfully");

            $this->logs[] = $this->getLogString("Fetching Grain Prices from grain_price_hub table");
            $grain_prices = $this->grainHubPrice->get_data();
            $this->logs[] = $this->getLogString(count($grain_prices) . " data fetched successfully");

            $this->logs[] = $this->getLogString("Fetching Data from cc_farm_loan table");
            $cc_farm_loan = $this->ccFarmLoan->get_data($harvest_members);
            $this->logs[] = $this->getLogString(count($cc_farm_loan) . " data fetched successfully");

            $this->logs[] = $this->getLogString("Grouping data based on unique_member_id and product_type on cleared records");
            $grouped_data = _group_by_two($cleared_data, "unique_member_id", "product_type");
            $this->logs[] = $this->getLogString(count($grouped_data) . " data grouped.");

            $members_to_update = array();


            $not_on_cleared = 0;

            foreach ($harvest_members as $cc_payment_member) {
                $unique_member_id = $cc_payment_member["unique_member_id"];
                $product = $cc_payment_member["product"];
                $key = get_key_concat($unique_member_id, $product);
                if (isset($grouped_data[$key])) {
                    $this->logs[] = $this->getLogString("Computing Data for Unique Member ID {$unique_member_id} and Product Type {$product}");
                    $member = $grouped_data[$key];
                    $hub_name = $member[0]["hub_name"];

                    list($processing_cost, $threshing_cost, $transporter_cost, $costs, $bags_marketed, $net_weight, $total_weight) =
                        __fetch_multiple_sum($member, [
                            "cc_processing_cost",
                            "threshing_cost",
                            "transporter_cost",
                            "costs",
                            "bags_marketed",
                            "net_weight",
                            "total_weight"
                        ]);

                    $transporter_cost = $this->calculateTransportCost($conn_transport, $unique_member_id);
                    $confirmThreshingData = $this->fetchConfirmThreshingData($conn_farming, $unique_member_id);
                    $harvestReceivingData = $this->fetchHarvestReceivingRecord($conn_inventory, $unique_member_id);

                    $threshing_cost = $this->threshingCalculation($unique_member_id, $confirmThreshingData, $harvestReceivingData);

                    // //Assigned the new value of costs to the variable.
                    $costs = $costs + $threshing_cost + $transporter_cost;

                    $cc_payment_member["no_of_bags_marketed"] = $bags_marketed;
                    $cc_payment_member["net_weight_marketed"] = $net_weight;
                    $cc_payment_member["threshing_cost"] = $threshing_cost;
                    $cc_payment_member["transport_cost"] = $transporter_cost;
                    $cc_payment_member["processing_cost"] = $processing_cost;
                    $cc_payment_member["total_cost"] = $costs;
                    $cc_payment_member["updated_flag"] = 1;
                    $cc_payment_member["misc_account"] = get_or_default($cc_farm_loan, get_key_concat($unique_member_id, $product), array())["misc_account"] ?? 0;
                    $cc_payment_member["loan_before_harvest"] = get_or_default($cc_farm_loan, get_key_concat($unique_member_id, $product), array())["loan_before_harvest"] ?? 0;

                    $this->logs[] = $this->getLogString("Total computed data:");

                    $this->logs[] = $this->getLogString("Bags marketed: {$bags_marketed}");
                    $this->logs[] = $this->getLogString("Net Weight marketed: {$net_weight}");
                    $this->logs[] = $this->getLogString("Threshing cost: {$threshing_cost}");
                    $this->logs[] = $this->getLogString("Transporter cost: {$transporter_cost}");
                    $this->logs[] = $this->getLogString("Processing cost: {$processing_cost}");
                    $this->logs[] = $this->getLogString("Misc Amount: {$cc_payment_member["misc_account"]}");
                    $this->logs[] = $this->getLogString("Loan before harvest: {$cc_payment_member["loan_before_harvest"]}");

                    $contractual_threshold = $cc_payment_member["no_of_bags_marketed"] /
                        ($cc_payment_member["field_size"] == 0 ? 1 : $cc_payment_member["field_size"]);

                    if ($contractual_threshold < $config->contractual_threshold_config) {
                        $cc_payment_member["contractual_flag"] = 1;
                        $cc_payment_member["exceeded_expectation"] = 0;
                    } else if ($contractual_threshold > $config->contractual_threshold_config) {
                        $cc_payment_member["exceeded_expectation"] = 1;
                        $cc_payment_member["contractual_flag"] = 0;
                    } else {
                        $cc_payment_member["contractual_flag"] = 0;
                        $cc_payment_member["exceeded_expectation"] = 0;
                    }

                    if (isset($grain_prices[get_key_concat($product, $hub_name)])) {
                        $price_per_kg = $grain_prices[get_key_concat($product, $hub_name)]["price"];
                        $value_of_grain = $price_per_kg * $net_weight;

                        $cc_payment_member["grain_value"] = $value_of_grain;

                        $this->logs[] = $this->getLogString("Grain price per kg: {$price_per_kg}");

                        $this->logs[] = $this->getLogString("Value of grain: {$value_of_grain}");

                        $harvest_advance = $value_of_grain - ($cc_payment_member["loan_before_harvest"] + $costs) + $cc_payment_member["misc_account"];

                        $this->logs[] = $this->getLogString("V.LBH.Cost.Misc: {$value_of_grain}, {$cc_payment_member["loan_before_harvest"]}, {$costs}, {$cc_payment_member["misc_account"]}");

                        $cc_payment_member["harvest_advance"] = $harvest_advance;

                        $members_to_update[] = $cc_payment_member;
                    } else {
                        $this->logs[] = $this->getLogString("Grain Price not found for Grain: {$product} and Hub: {$hub_name}");
                    }
                } else {
                    $unique_member_id = $cc_payment_member["unique_member_id"];

                    $transporter_cost = $this->calculateTransportCost($conn_transport, $unique_member_id);
                    $confirmThreshingData = $this->fetchConfirmThreshingData($conn_farming, $unique_member_id);
                    $harvestReceivingData = $this->fetchHarvestReceivingRecord($conn_inventory, $unique_member_id);

                    $threshing_cost = $this->threshingCalculation($unique_member_id, $confirmThreshingData, $harvestReceivingData);

                    $threshing_cost = 0;
                    $cc_payment_member["transport_cost"] = $transporter_cost;
                    $cc_payment_member["threshing_cost"] = $threshing_cost;
                    $cc_payment_member["total_cost"] = $threshing_cost + $transporter_cost;
                    $cc_payment_member["updated_flag"] = 1;
                    $cc_payment_member["misc_account"] = get_or_default($cc_farm_loan, get_key_concat($unique_member_id, $product), array())["misc_account"] ?? 0;
                    $cc_payment_member["loan_before_harvest"] = get_or_default($cc_farm_loan, get_key_concat($unique_member_id, $product), array())["loan_before_harvest"] ?? 0;
                    $harvest_advance = $cc_payment_member["misc_account"] - ($cc_payment_member["loan_before_harvest"] + $cc_payment_member["total_cost"]);
                    $cc_payment_member["harvest_advance"] = $harvest_advance;
                    $members_to_update[] = $cc_payment_member;
                    $not_on_cleared += 1;
                }
            }

            $this->logs[] = $this->getLogString("$not_on_cleared members are not on cleared records");


            $this->logs[] = $this->getLogString("Updating " . count($members_to_update) . " data into harvest members payment");

            $this->harvestMemberPayment->insert_many($members_to_update, 2000);

            $this->logs[] = $this->getLogString(count($members_to_update) . " data has been updated in harvest members payment");

            $ik_numbers = array_map(array($this, "getIkNumber"), $members_to_update);

            if (count($ik_numbers) > 0) {
                $harvest_iks = $this->harvestMemberPayment->get_harvest_advance($ik_numbers);

                $grouped_data = $this->group_harvest_advance($harvest_iks);

                $this->logs[] = $this->getLogString("Computing harvest advance for updated data");

                $harvest_advance_update = array();
                foreach ($members_to_update as $member) {
                    $key = $member["ik_number"];
                    $harvest_advance = $member["harvest_advance"];
                    if ($harvest_advance > 0) {
                        $percentage_ownership = round($harvest_advance / $grouped_data[$key]["positive"], 9);
                        $shared_debt = round($percentage_ownership * $grouped_data[$key]["negative"], 2);
                        $net_harvest_advance = round($harvest_advance + $shared_debt, 2);
                        $member["percentage_ownership"] = $percentage_ownership;
                        $member["shared_debt"] = $shared_debt;
                        $member["net_harvest_advance"] = $net_harvest_advance;
                        $this->logs[] = $this->getLogString($member["unique_member_id"] . " Percentage Ownership: {$percentage_ownership}");
                        $this->logs[] = $this->getLogString($member["unique_member_id"] . " Shared Debt: {$shared_debt}");
                        $this->logs[] = $this->getLogString($member["unique_member_id"] . " Net Harvest Advance: {$net_harvest_advance}");
                    } else {
                        $member["shared_debt"] = 0;
                        $member["net_harvest_advance"] = 0;
                        $member["percentage_ownership"] = 0;
                        $this->logs[] = $this->getLogString($member["unique_member_id"] . " has a negative harvest advance");
                    }
                    $harvest_advance_update[] = $member;
                }

                $this->logs[] = $this->getLogString("Updating " . count($harvest_advance_update) . " data into harvest members payment");

                $this->harvestMemberPayment->insert_many($harvest_advance_update, 2000);

                $this->logs[] = $this->getLogString(count($harvest_advance_update) . " data has been updated in harvest members payment");
            }


            $endTime = microtime(true);

            $timeElapsed = ($endTime - $startTime);
            $this->logs[] = $this->getLogString("Computation ran in {$timeElapsed}s");

            $this->compute_soy_data($config, $grain_prices);
            $soyEndTime = microtime(true);

            $timeElapsed = ($soyEndTime - $endTime);
            $this->logs[] = $this->getLogString("Soy Computation ran in {$timeElapsed}s");
        } catch (Exception $exception) {
            $this->logs[] = $this->getLogString("Error occurred: {$exception->getMessage()}");
        }

        $endTime = microtime(true);

        $timeElapsed = ($endTime - $startTime);
        $this->logs[] = $this->getLogString("Job ran in {$timeElapsed}s");

        return $this->logs;
    }

    private function getLogString($message): string
    {
        return getLogString("CRON 2", $message);
    }

    private function getIkNumber($record)
    {
        $ik_number = $record["ik_number"];
        return "'$ik_number'";
    }

    private function group_harvest_advance(array $harvest_iks): array
    {
        $grouped = array();

        foreach ($harvest_iks as $member) {
            $key = $member["ik_number"];
            if (!isset($grouped[$key])) {
                $grouped[$key]["positive"] = 0;
                $grouped[$key]["negative"] = 0;
            }
            $harvest_advance = $member["harvest_advance"];
            if ($harvest_advance < 0) {
                $grouped[$key]["negative"] += $harvest_advance;
            } else {
                $grouped[$key]["positive"] += $harvest_advance;
            }
        }

        return $grouped;
    }

    private function initialDataComputation()
    {
        $this->harvestMemberPayment->delete_soy_product();
        $this->harvestMemberPayment->reset_deleted_data();
        $this->harvestMemberPayment->reset_update_flag();
        $this->clearedDbOperations->set_transfer_flag_1();
    }

    private function compute_soy_data($config, $grain_prices,)
    {
        $this->logs[] = $this->getLogString("Fetching Data from cleared_records table for soy computation");
        $this->harvestMemberPaymentSoy->reset_deleted_data();

        $soy_data = $this->clearedDbOperations->get_soy_data();

        $this->logs[] = $this->getLogString(count($soy_data) . " data fetched successfully");

        $members_to_update = array();

        foreach ($soy_data as $member) {
            $cc_payment_member = array();

            $unique_member_id = $member[0]["unique_member_id"];
            $product = $member[0]["product_type"];
            $variety = $member[0]["variety"];

            $this->logs[] = $this->getLogString("Computing Data for Unique Member ID {$unique_member_id} and Variety {$variety}");
            $hub_name = $member[0]["hub_name"];

            list($processing_cost, $threshing_cost, $transporter_cost, $costs, $bags_marketed, $net_weight, $total_weight) =
                __fetch_multiple_sum($member, [
                    "cc_processing_cost",
                    "threshing_cost",
                    "transporter_cost",
                    "costs",
                    "bags_marketed",
                    "net_weight",
                    "total_weight"
                ]);



            $cc_payment_member["unique_member_id"] = $unique_member_id;
            $cc_payment_member["variety"] = $variety;
            $cc_payment_member["no_of_bags_marketed"] = $bags_marketed;
            $cc_payment_member["net_weight_marketed"] = $net_weight;
            $cc_payment_member["threshing_cost"] = $threshing_cost;
            $cc_payment_member["transport_cost"] = $transporter_cost;
            $cc_payment_member["processing_cost"] = $processing_cost;
            $cc_payment_member["ik_number"] = $member[0]["ik_number"];
            $cc_payment_member["total_cost"] = $costs;
            $cc_payment_member["updated_flag"] = 1;
            $cc_payment_member["misc_account"] = 0;
            $cc_payment_member["loan_before_harvest"] = 0;
            $cc_payment_member["season"] = $config->season_config;
            $this->logs[] = $this->getLogString("Total computed data:");

            $this->logs[] = $this->getLogString("Bags marketed: {$bags_marketed}");
            $this->logs[] = $this->getLogString("Net Weight marketed: {$net_weight}");
            $this->logs[] = $this->getLogString("Threshing cost: {$threshing_cost}");
            $this->logs[] = $this->getLogString("Transporter cost: {$transporter_cost}");
            $this->logs[] = $this->getLogString("Processing cost: {$processing_cost}");

            $price_per_kg = 0;

            $variety = strtolower($member[0]["variety"]);

            if (strpos($variety, "silver") !== false) {
                $price_per_kg = $config->silver_soy;
            } elseif (strpos($variety, "mai") !== false && strpos($variety, "idon") !== false) {
                $price_per_kg = $config->mai_idon_fara_soy;
            }
            $value_of_grain = $price_per_kg * $net_weight;

            $cc_payment_member["grain_value"] = $value_of_grain;

            $this->logs[] = $this->getLogString("Grain price per kg: {$price_per_kg}");

            $this->logs[] = $this->getLogString("Value of grain: {$value_of_grain}");

            $harvest_advance = $value_of_grain - $costs;

            $this->logs[] = $this->getLogString("Harvest Advance: {$harvest_advance}");

            $cc_payment_member["harvest_advance"] = $harvest_advance;

            $members_to_update[] = $cc_payment_member;
        }

        $this->logs[] = $this->getLogString("Updating " . count($members_to_update) . " data into harvest members payment soy");

        $this->harvestMemberPaymentSoy->insert_many($members_to_update, 2000);

        $this->logs[] = $this->getLogString(count($members_to_update) . " data has been updated in harvest members payment soy");
    }

    function fetchConfirmThreshingData($conn, $uniqueMemberId)
    {
        $query = "select unique_member_id, sum(field_size), sum(cost) as cost from (

            SELECT unique_member_id, field_size, field_size * 5990 AS cost, ik_number
                              FROM public.confirm_threshing
                              WHERE delete_flag = 0 AND type_of_thresher = 'COMPANY' AND unique_field_id NOT IN (SELECT unique_field_id FROM manual_confirm_threshing WHERE LOWER(type_of_thresher) = 'company')
                              
                union all
                              
            SELECT unique_member_id, field_size, field_size * 5990 AS cost, ik_number
                    FROM public.manual_confirm_threshing
                    WHERE delete_flag = 0 AND LOWER(type_of_thresher) = 'company') as u
                    WHERE u.unique_member_id = ?
                    group by u.unique_member_id";

        $stmt = $conn->prepare($query);
        $stmt->execute([$uniqueMemberId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    function fetchHarvestReceivingRecord($conn, $uniqueMemberId)
    {
        $query = "SELECT unique_member_id, SUM(total_field_size_threshed), SUM(total_field_size_threshed) * 5990 AS cost
                  FROM harvest_receiving_record
                  WHERE threshing_flag = 1 AND unique_member_id = ?
                  GROUP BY unique_member_id";

        $stmt = $conn->prepare($query);
        $stmt->execute([$uniqueMemberId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    private function calculateTransportCost($conn, $uniqueMemberId)
    {
        $query = "
            SELECT SUM(transportation_cost) AS total_cost
            FROM shipment
            WHERE shipment_status = 'DELIVERED'
            AND created_at >= '2024-10-01' 
            AND unique_member_id = ?
        ";

        $stmt = $conn->prepare($query);
        $stmt->execute([$uniqueMemberId]);

        // Fetch the result as an associative array, then check for the total_cost value
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        // If result is null (no rows), return 0, else return the total_cost as an integer
        return $result ? (int)$result['total_cost'] : 0;
    }


    function threshingCalculation($uniqueMemberId, $confirmThreshingData, $harvestReceivingData)
    {

        $confirmThreshingCost = 0;
        $harvestReceivingCost = 0;


        foreach ($confirmThreshingData as $ctData) {
            try {
                if ($ctData['unique_member_id'] == $uniqueMemberId) {
                    $confirmThreshingCost = $ctData['cost'];
                    $this->logs[] = $this->getLogString("Merged Threshing Cost: " . $confirmThreshingCost);
                    break;
                }
            } catch (Exception $th) {

                //throw $th;
            }
        }

        foreach ($harvestReceivingData as $hrData) {
            try {
                if ($hrData['unique_member_id'] == $uniqueMemberId) {
                    $harvestReceivingCost = $hrData['cost'];
                    $this->logs[] = $this->getLogString("Harvest Receiving Cost: " . $harvestReceivingCost);
                    break;
                }
            } catch (Exception $th) {
            }
        }

        if ($confirmThreshingCost === $harvestReceivingCost) {
            $threshing_cost = $confirmThreshingCost;
            $this->logs[] = $this->getLogString("Threshing cost is equal for both, selected cost: " . $threshing_cost);
        } elseif ($harvestReceivingCost >= $confirmThreshingCost) {
            $threshing_cost = $harvestReceivingCost;
            $this->logs[] = $this->getLogString("Harvest Receiving Cost is greater, selected cost: " . $threshing_cost);
        } else {
            $threshing_cost = $confirmThreshingCost;
            $this->logs[] = $this->getLogString("Merged Threshing Cost is selected: " . $threshing_cost);
        }

        return $threshing_cost;
    }
}
