<?php
require_once(__DIR__ . "/../constants.php");
require_once(__DIR__ . "/../connect_db.php");
require_once(__DIR__ . "/../app/models/float_cards.php");
require_once(__DIR__ ."/../app/models/member_cards.php");
require_once(__DIR__ ."/../app/models/wedi_lmr_cards.php");
require_once(__DIR__ . "/../app/models/payment_secrets.php");
require_once(__DIR__ . "/../app/models/exception.php");
require_once(__DIR__ . "/../app/controllers/__response_helpers.php");
require_once(__DIR__ . "/../data/bank_codes.php");
require_once(__DIR__ . "/../app/controllers/Logger.php");

ini_set('max_execution_time', '300');


if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] != 'GET') {
    http_response_code(403);
    echo (json_encode(setResponse(false, "Something went wrong", [], "Request Method Not Allowed")));
    exit;
}

/**
 * Summary of generateRecipientId
 * @param mixed $bank_list
 * @param mixed $userDataClass
 * @return void
 * Adjust the column name for float config
 */
function generateRecipientId($bank_list, $userCardDetails)
{
    try {
        $driver = new DBDriver;
        $con = $driver->connectPgSql(PG_FINANCE_DB_NAME);

        $pending_users = $userCardDetails::selectPending($con);
        $configs = PaymentSecrets::selectMany($con);

        $responseArray = [];

        // LOOP THROUGH USER CARDS LIST GENERATING ids FOR EACH AND UPDATING THE USER CARDS TABLE ACCORDINGLY
        foreach ($pending_users as $one_user) {

            $result = generateID($one_user, $bank_list, $configs);
            if ($result->status) {
                $userCardDetails::updateOne($con, $one_user['id'],
                    [
                        "recipient_id" => $result->data->recipient_code,
                        "status_message" => $result->message,
                        "updated_at" => date('Y-m-d H:i:s'),
                        "profiling_attempts" => $one_user['profiling_attempts'] + 1
                    ]);
                $responseArray[] = [
                    "success" => true,
                    "id" => $one_user["id"],
                    "account_number" => $one_user["account_number"],
                    "bank_name" => $one_user["bank_name"],
                    "message" => $result->message,
                    "generated_id" => $result->data->recipient_code,
                    "attempts" => $one_user["profiling_attempts"] + 1
                ];

            } else {
                $userCardDetails::updateOne($con, $one_user['id'],
                    ["status_message" => $result->message,
                        "updated_at" => date('Y-m-d H:i:s'),
                        "profiling_attempts" => $one_user['profiling_attempts'] + 1
                    ]);
                $responseArray[] = [
                    "success" => false,
                    "id" => $one_user["id"],
                    "account_number" => $one_user["account_number"],
                    "bank" => $one_user["bank_name"],
                    "bank_code" => $bank_list[$one_user['bank_name']] ?? $bank_list['default'],
                    "message" => $result->message,
                    "attempts" => $one_user["profiling_attempts"] + 1
                ];
            }
        }
    } catch (Exception $e) {
        $responseArray[] = [
            "success" => false,
            "id" => $one_user["id"],
            "account_number" => $one_user["account_number"],
            "message" => $e->getMessage()
        ];
    }
    echo json_encode($responseArray);
    $con = null;
}

//For Float Cards
generateRecipientId($banks, FloatCards::class);

//For Member Cards
generateRecipientId($banks, MemberCards::class);

//For Wedi LMR Cards
generateRecipientId($banks, WediLMRCards::class);


function generateID($record, $banks, $configs)
{
    $payload = array(
        "type" => "nuban",
        "name" => $record['account_name'],
        "account_number" => $record['account_number'],
        "bank_code" => $banks[$record['bank_name']] ?? $banks['default'],
        "currency" => "NGN",
        "description" => $record['id']
    );

    
    $data_string = json_encode($payload);
    
    $ch = curl_init(PAYSTACK_BASE_URL . '/transferrecipient');
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt(
        $ch,
        CURLOPT_HTTPHEADER,
        array(
            'Content-Type: application/json',
            'Authorization: Bearer '. trim($configs->PAYSTACK_API_KEY),
            'Content-Length: ' . strlen($data_string)
        )
    );
    $response = curl_exec($ch);
    print_r($response);
    curl_close($ch);
    return  json_decode($response);
}
?>
