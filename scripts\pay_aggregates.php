<?php
require_once (__DIR__.'/../connect_db.php');
require_once (__DIR__.'/../scripts/pay_recipient.php');
require_once (__DIR__.'/../app/controllers/__response_helpers.php');
require_once (__DIR__.'/../app/models/harvest_tg_payments.php');
require_once (__DIR__.'/../app/models/payment_queue.php');
require_once (__DIR__.'/../app/models/app_logs.php');
require_once (__DIR__.'/../app/models/payment_config.php');


ini_set('max_execution_time', '300');
date_default_timezone_set('Africa/Lagos');


if(isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] == 'GET') {
    $user = validateUser();
    if(!$user){
        http_response_code(401);
        echo(json_encode(setResponse(false,"Something went wrong", [], "Invalid request. User could not be authenticated")));
        exit;
    }
    }
    
    if(isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] != 'GET') {
        http_response_code(403);
        echo(json_encode(setResponse(false,"Something went wrong", [], "Request Method Not Allowed")));
        exit;
    }
      (function (){
        try{
        $driver = new DBDriver;
        $conn = $driver->connectPgSql(PG_FINANCE_DB_NAME);

        $harvest_config = PaymentConfig::selectOne($conn,'aggregate_payment');

        if($harvest_config['payment_flag'] == 0
           || strtolower(date('Y-m-d H:i:s a')) > strtolower($harvest_config['pay_until'])
           || !str_contains(strtolower($harvest_config['repeat_every']),strtolower(date('l')))
           || strtolower($harvest_config['time_of_payment']) != date('h:i a')
        ){
            echo(json_encode(setResponse(false,"Not quite...", [], "Outside payment hours!!! Aggregate payments cannot be made at this time.")));
            exit;
        }
        $payments = PaymentQueue::selectPending($conn,'aggregate_payment');
        $responseArray = [];
        $deleteArray = [];
        foreach($payments as $one_payment){
            try{
                $payload = json_decode($one_payment['payload']);
                $needed_payload = extractPayload($payload);
               $result = payOne($needed_payload);
               if($result->status){
                HarvestTransactions::insertOne($conn,
                [
                    "reference"=>$needed_payload['reference'],
                    "payee_id"=>$payload->payee_id,
                    "recipient_id" => $result->data->recipient_code ?? 'recipient_id',
                    "payment_type" => $payload->payment_type,
                    "amount" => $needed_payload['amount']/100,
                    "code"=> $result->data->transfer_code ?? 'transfer_code',
                    "transaction_date" => date('Y-m-d H:i:s'),
                    "description" => $result->data->reason ?? 'reason',
                ]);
                $deleteArray[] = $one_payment['id'];

                $responseArray[] = [
                    "success"=>true,
                    "account_number"=> $payload->account_number,
                    "amount"=> $needed_payload['amount']/100,
                    "recipient"=> $needed_payload['recipient'],
                    "message"=>$result->message,
                    "reference"=>$needed_payload['reference'],
                    "attempts"=>$one_payment['attempts'] + 1
                    ];

            }
            else{
                PaymentQueue::updateOne($conn, $one_payment['id'], 
                [
                "extra_info"=>$result->message,
                "attempts" => $one_payment['attempts'] + 1,
                "last_attempted_at"=> date('Y-m-d H:i:s')
                ]);
                $responseArray[] = [
                    "success"=>false,
                    "account_number"=> $payload->account_number,
                    "amount"=> $needed_payload['amount']/100,
                    "recipient_id"=> $needed_payload['recipient'],
                    "payee_id"=>$payload->payee_id,
                    "message"=>$result->message,
                    "reference"=>$needed_payload['reference'],
                    "attempts"=>$one_payment['attempts'] + 1
                    ];
            }   
            }
               
            catch(Exception $e){
            $responseArray[] = [
                "success"=>false,
                "reference"=> $needed_payload['reference'],
                "account_number"=> $payload->account_number,
                "message"=>$e->getMessage()
                ];
        }
        }
    }
        catch(Exception $e){
            $responseArray[] = json_encode(setResponse(false,"something went wrong...", [], $e->getMessage()));
            }
            echo json_encode($responseArray);
            if(count($deleteArray) > 0) PaymentQueue::deleteMany($conn,$deleteArray);
            // WE COULD WRITE TO A FILE BEFORE REMOVING FROM THE QUEUE
            $conn = null;
    })()

?>