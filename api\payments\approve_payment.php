<?php
require_once(__DIR__ . '/../../constants.php');
require_once(__DIR__ . '/../../connect_db.php');
require_once(__DIR__ . '/../auth.php');
require_once(__DIR__ . '/../../app/models/exception.php');
require_once(__DIR__ . '/../../app/models/payment_queue.php');
require_once(__DIR__ . '/../../app/models/app_logs.php');
require_once(__DIR__ . '/../payment_helper.php');
require_once(__DIR__ . '/../../app/controllers/cors.php');
require_once(__DIR__ . '/../../app/models/payment_tracker.php');

cors();

function approvePayment()
{
    try {
        $user = validateUser();

        $driver = new DBDriver;
        $conn = $driver->connectPgSql(PG_FINANCE_DB_NAME);
        $conn_inventory = $driver->connectPgSql(PG_INVENTORY_DB_NAME);

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            throw new CustomException(403, "Request Method Not Allowed");
        }

        $json = file_get_contents('php://input');
        $data = json_decode($json, true);

        if (!isset($data['reference_ids']) || !is_array($data['reference_ids'])) {
            throw new CustomException(400, "Reference ID(s) missing or not in the correct format");
        }

        $approver_name = $user->data->staff_name;
        $approver_role = $user->data->config[0]->permission_name;
        $approve_config = false;
        if (isset($user->data->config[0]->configs)) {
            $canApprove = array_filter($user->data->config[0]->configs, function($config) {
                return $config->config_name === 'canApprovePayment' && $config->config_value === true;
            });
    
            if (!empty($canApprove)) {
                $approve_config = true;
            }
        }

        foreach ($data['reference_ids'] as $reference_id) {
            $stmtCheck = $conn->prepare("SELECT initiator_name, initiator_role FROM payment_tracker WHERE reference_id = ?");
            $stmtCheck->execute([$reference_id]);
            $result = $stmtCheck->fetch();

            $stmtCheck1 = $conn->prepare("SELECT approval_flag FROM payment_queue WHERE reference = ?");
            $stmtCheck1->execute([$reference_id]);
            $result1 = $stmtCheck->fetch();

            if ($result1 && $result1['approval_flag'] == 1) {
                throw new CustomException(403, "This transaction has been previously approved");
            }
            
            //Check if user is authorized to approve payment
            if (!$approve_config) {
                throw new CustomException(403, "You do not have the permission to approve payments.");
            }

            //Check to ensure the person that initiated isn't approving
            if ($result['initiator_name'] == $approver_name && $result['initiator_role'] == $approver_role) {
                throw new CustomException(403, "You cannot approve a transaction you initiated.");
            }

        }

        $reference_ids_array = '{' . implode(',', $data['reference_ids']) . '}';
        $stmt1 = $conn->prepare("UPDATE payment_queue SET approval_flag = 1 WHERE reference = ANY(:reference_ids)");
        $stmt1->execute([':reference_ids' => $reference_ids_array]);


        //Update into payment_tracker table
        $currentTime = date('Y-m-d H:i:s');
        foreach ($data['reference_ids'] as $reference_id) {
            $stmt = $conn->prepare("UPDATE payment_tracker SET approver_name = ?, approver_role = ?, approved_at = ?, updated_at = ? WHERE reference_id = ?");
            $stmt->execute([$approver_name, $approver_role, $currentTime, $currentTime, $reference_id]);
        }

        foreach ($data['reference_ids'] as $reference_id) {
            // Fetch action and payee_id for each reference_id
            $stmtQueue = $conn->prepare("SELECT action, payee_id FROM payment_queue WHERE reference = ?");
            $stmtQueue->execute([$reference_id]);
            $result = $stmtQueue->fetch();

            if ($result) {
                $action = $result['action'];
                $payee_id = $result['payee_id'];

                if ($action == 'Company Transporter' || $action == 'Independent Transporter') {
                    PaymentTrackerHelper::updateTransportController($conn_inventory, $payee_id);
                }
            }
        }


        echo json_encode(['status' => true, 'message' => 'Payments approved successfully!']);

        $logMessage = "APPROVED PAYMENT for recipients.";
        AppLogs::insertOne($conn, generateLogs($user->data->staff_id, $logMessage, $data));
    } catch (CustomException $e) {
        http_response_code($e->status ?? 500);
        echo json_encode(['status' => false, 'message' => $e->getMessage()]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['status' => false, 'message' => "An unexpected error occurred: " . $e->getMessage()]);
    }
}

approvePayment();
