<?php
require_once '../connect_db.php';
require_once '../constants.php';
require_once("../app/db/config/ConfigDbOperations.php");
require_once '../app/controllers/cors.php';
require_once '../app/controllers/payment_status.php';
require_once 'payment_helper.php';
require_once ('auth.php');

cors();

$user = validateUser();

$json = file_get_contents('php://input');
$data = json_decode($json, true);

$response = get_soy_payment();

if (is_array($response['data'])){
    $response['data'] = array_values(array_filter($response['data'], 'filter_unpaid'));
}

echo json_encode($response);

?>