<?php
require_once(__DIR__ . "/../app//controllers/__checks.php");
require_once(__DIR__ . "/../app//controllers/__data_helpers.php");
require_once(__DIR__ . "/../app/models/harvest_checking_records.php");
require_once(__DIR__ . "/../app/models/harvest_scaling_records.php");
require_once(__DIR__ . "/../app/models/harvest_cleared_records.php");
require_once(__DIR__ . "/../app/models/harvest_receiving_records.php");
require_once(__DIR__ . "/../app/controllers/Logger.php");

/**
 * Cron 0 serves the purpose of reviewing records on the harvest receiving records, harvest scaling records and harvest checking records
 * in the event there are no differences in the records, Cron 0 copies the records into the harvest cleared records. 
 * 
 * Observing the block of code below, two arrays are initialized. 
 * $passed_disparity_check holds all HSFs where the system didn't find a difference between the harvest receiving, scaling and checking records, failed_disparity_check is the exact opposite
 * $passed_quality_check holds all HSFs where the system did not find any of the quality metrics ( mold, moisture, cleanliness) that exceeded thresholds in the collection center config, $failed_quality_checks did the exact opposite
 * 
 */

function executeCron0($conn_1, $conn_2, $config, $conn_3)
{
  $start = microtime(true);
  echo  date('Y-m-d H:i:s.u') . "  🎶🎶🎶  =====CRON 0 STARTED==== 🎶🎶🎶 <br /> \n";
  $logArray = [];
  $errorLogs = []; // Store logs for batch insertion

  try {
    echo  date('Y-m-d H:i:s.u') . " ✋✋......Now fetching Initial Data... ✋✋ <br /> \n";
    $scaling_records = CheckingRecords::getScalingRecords($conn_1);
    $receiving_records = CheckingRecords::getReceivingRecords($conn_1);

    echo "---------------------------------\n";
    echo  date('Y-m-d H:i:s.u') . " ✔✔✔✔ ......Done fetching Initial Data...✔✔✔✔ <br /> \n";

    $allConfirmedFieldIds = getAllUniqueFieldIdsFromCCThreshingInfo($conn_1);

    $fieldIdToThresherMapping = fetchFieldIdToThresherMapping($conn_2);
 
    $passed_disparity_check = [];
    $failed_receiver_disparity_check = [];
    $failed_scaler_disparity_check = [];
    $passed_quality_check = [];
    $failed_quality_check = [];
    $scalingWaybillIdsFromFailedReceiving = [];
    $receivingWaybillIdsFromFailedScaling = [];
    $scalingWaybillIdsFromReceiving = [];

    /***
     * Based on the conversation and database mapping, here is the trick here
     * Run the scaling disparity check. If it fails, push to the array and store the receiving waybill ids
     * Else push the passed disparity check array
     * Run the receiving disparity check but first check for and ignore the flagged receiving waybill ids
     * Flag the scaling waybill ids of thos that fails the receiving disparity check
     * Only run quality check for the scaling records whose waybill ids are not flagged
     */

    foreach ($scaling_records as $record) {
      $scalingDisparity = __failedScalingDisparityCheck($record);

      if (!empty($scalingDisparity)) {
        $errorLogs[] = [
          'waybill_id' => $record["scaling_waybill_id"],
          'check_type' => 'scaling_disparity',
          'reason' => implode(", ", $scalingDisparity),
        ];
        array_push($failed_scaler_disparity_check, $record);

        foreach (explode(',', $record['receiving_waybill_ids']) as $id) {
          $receivingWaybillIdsFromFailedScaling[] = $id;
        }
      } else {
        $passed_disparity_check[] = $record;
      }
    }

    foreach ($receiving_records as $record) {
      $scalingWaybillIdsFromReceiving[] = $record['scaling_waybill_id'];

      if (in_array($record['receiving_waybill_id'], $receivingWaybillIdsFromFailedScaling)) {
        continue;
      }

      $receivingDisparity = __failedReceivingDisparityCheck($record, $allConfirmedFieldIds, $fieldIdToThresherMapping);

      if (!empty($receivingDisparity)) {
        $errorLogs[] = [
          'waybill_id' => $record["receiving_waybill_id"],
          'check_type' => 'receiving_disparity',
          'reason' => implode(", ", $receivingDisparity),
        ];
        array_push($failed_receiver_disparity_check, $record);
        $scalingWaybillIdsFromFailedReceiving[] = $record['scaling_waybill_id'];
      }
    }

    foreach ($passed_disparity_check as $record) {
      if (in_array($record['scaling_waybill_id'], $scalingWaybillIdsFromFailedReceiving)) {
        continue;
      }

      if (!in_array($record['scaling_waybill_id'], $scalingWaybillIdsFromReceiving)) {
        continue;
      }

      $qualityCheck = __passedQualityCheck($record, $config);

      if (!empty($qualityCheck)) {
        $errorLogs[] = [
          'waybill_id' => $record["scaling_waybill_id"],
          'check_type' => 'quality_check',
          'reason' => implode(", ", $qualityCheck),
        ];
        array_push($failed_quality_check, $record);
      } else {
        array_push($passed_quality_check, $record);
      }
    }

    //What is to be updated for failed quality checks
    // DATABASE UPDATES AND INSERTS
    echo  date('Y-m-d H:i:s.u') . " ✋✋......Now running db updates and inserts...✋✋ <br /> \n";
    ClearedRecords::batchLogFailures($conn_3, $errorLogs);
    ReceivingRecords::updateMany($conn_1, $failed_receiver_disparity_check, ["verifier_flag" => 1]);
    ScalingRecords::updateMany($conn_1, $failed_scaler_disparity_check, ["verifier_flag" => 1]);
    ScalingRecords::updateMany($conn_1, $passed_quality_check, ["passed_quality_check" => 1]);
    ClearedRecords::insertMany($conn_1, $passed_quality_check, 1600, $config);
    echo  date('Y-m-d H:i:s.u') . " ✔✔✔✔......Done running db updates and inserts... ✔✔✔✔ <br /> \n";


    // STRICTLY LOGS
    //LOG 10  RECORDS FOR EACH CATEGORY
    for ($i = 0; $i < 10; $i++) {
      if ($i >= count($scaling_records)) break;
      $logArray[] = getLogString("Cron 0 scaling records",  $scaling_records[$i]['scaling_waybill_id'] . " - Was initially fetched");
    }

    for ($i = 0; $i < 10; $i++) {
      if ($i >= count($receiving_records)) break;
      $logArray[] = getLogString("Cron 0 receiving records",  $receiving_records[$i]['receiving_waybill_id'] . " - Was initially fetched");
    }

    for ($i = 0; $i < 10; $i++) {
      if ($i >= count($passed_disparity_check)) break;
      $logArray[] = getLogString("Cron 0",  $passed_disparity_check[$i]['scaling_waybill_id'] . " - Passed Disparity Check");
    }

    for ($i = 0; $i < 10; $i++) {
      if ($i >= count($failed_scaler_disparity_check)) break;
      $logArray[] = getLogString("Cron 0",  $failed_scaler_disparity_check[$i]['scaling_waybill_id'] . " - Failed Scaling Disparity Check");
    }

    for ($i = 0; $i < 10; $i++) {
      if ($i >= count($failed_receiver_disparity_check)) break;
      $logArray[] = getLogString("Cron 0",  $failed_receiver_disparity_check[$i]['scaling_waybill_id'] . " - Failed Receiving Disparity Check");
    }

    for ($i = 0; $i < 10; $i++) {
      if ($i >= count($passed_quality_check)) break;
      $logArray[] = getLogString("Cron 0",  $passed_quality_check[$i]['scaling_waybill_id'] . " - Passed Quality Check");
    }

    for ($i = 0; $i < 10; $i++) {
      if ($i >= count($failed_quality_check)) break;
      $logArray[] = getLogString("Cron 0",  $failed_quality_check[$i]['scaling_waybill_id'] . " - Failed Quality Check");
    }

    $end = microtime(true);
    $elapsed = $end - $start;

    $logArray[] = getLogString("CRON 0",  "🎵🎵🎵 Bravo!!!! CRON 0 ran successfully in " . $elapsed . " seconds 🎵🎵🎵");
    $logArray[] = getLogString("CRON 0", '🔊🔊🔊🔊🔊 - JOB SUMMARY - 🔊🔊🔊🔊🔊');
    $logArray[] = getLogString("CRON 0", '✍✍ - ' .  count($scaling_records) . " - Total initial scaling records===");
    $logArray[] = getLogString("CRON 0", '✍✍ - ' .  count($receiving_records) . " - Total initial receiving records===");
    $logArray[] = getLogString("CRON 0", '✍✍ - ' .  count($passed_disparity_check) . " - records passed Disparity Check===");
    $logArray[] = getLogString("CRON 0", '✍✍ - ' .  count($failed_scaler_disparity_check) . " - records failed Scaling Disparity Check===");
    $logArray[] = getLogString("CRON 0", '✍✍ - ' .  count($failed_receiver_disparity_check) . " - records failed Receiving Disparity Check===");
    $logArray[] = getLogString("CRON 0", '✍✍ - ' .  count($passed_quality_check) . " - records passed Quality Check===");
    $logArray[] = getLogString("CRON 0", '✍✍ - ' .  count($failed_quality_check) . " - records failed Quality Check===");
    $logArray[] = getLogString("CRON 0",  "👏👏👏 CRON 0 SAYS BYE...👏👏👏👏");

    foreach ($logArray as $log) {
      echo $log;
    }

    // END OF LOGS
  } catch (Exception $err) {
    foreach ($logArray as $log) {
      echo $log;
    }
    echo " 🙈🙈🙈...Something went wrong. Cron 0 failed: 🙈🙈🙈 " . $err->getMessage();
  }
}
