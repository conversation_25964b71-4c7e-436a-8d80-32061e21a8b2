<?php

require_once(__DIR__ . '/../auth.php');
require_once '../../app/controllers/cors.php';
require_once(__DIR__ . '/../../constants.php');
require_once(__DIR__ . '/../../connect_db.php');
require_once(__DIR__ . '/../../app/models/app_logs.php');
require_once (__DIR__.'/../../app/models/exception.php'); 

cors();

/* downloadMembersPayTemplate : Prepares a csv template for users to download 
* It validates the user to enable access to download
* It prepares a csv template based on the columns array and sample data for the user to have an idea of what to upload
* returns - null
*/

function downloadMembersPayTemplate()
{
    try {
        $user = validateUser();

        if($_SERVER['REQUEST_METHOD'] != 'GET') throw new CustomException(403,"Request Method Not Allowed");

        $driver = new DBDriver;
        $conn = $driver->connectPgSql(PG_FINANCE_DB_NAME);

        $filename = "harvest_member_pay_template.csv";
        $columns = ['ik_number', 'payee_name', 'id_loan_size', 'no_of_bags_marketed', 'total_grain_value', 'net_harvest_advance', 'amount_paid', 'payment_risk', 'recipient_id', 'account_number', 'account_name', 'bank_name'];

        $sampleData1 = ['**********', 'Musa Basil', 2.00, 40, 840096, 251000, 150000, 'NONE', 'RCP_353j3jfnvnzt6vz', '**********', 'Musa Basil', 'Access Bank'];
        $sampleData2 = ['**********', 'Ahmed Yinusa', 3.50, 20, 850086, 200000, 100000, 'FINANCE DNP', 'RCP_333j3jfnvnzt6vz', '**********', 'Ahmed Basil', 'UBA Bank'];
        $sampleData3 = ['**********', 'Isa Jacob', 1.00, 35, 740000, 251500, 158000, 'SHP DNP', 'RCP_335j3kfnvnzt6vz', '**********', 'Isa Yinus', 'Guaranty Trust Bank'];

        $tempFile = fopen('php://temp', 'w+');

        fputcsv($tempFile, $columns);
        fputcsv($tempFile, $sampleData1);
        fputcsv($tempFile, $sampleData2);
        fputcsv($tempFile, $sampleData3);
        rewind($tempFile);

        header('Content-Type: text/csv');
        header("Content-Disposition: attachment;filename=\"$filename\"");
        fpassthru($tempFile);

        fclose($tempFile);

        AppLogs::insertOne($conn, generateLogs($user->data->staff_id, "USER DOWNLOADED HARVEST MEMBER PAY TEMPLATE", $filename));

    } catch (CustomException $e) {
        http_response_code($e->status ?? 500);
        echo json_encode(['status' => false, 'message' => $e->getMessage()]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['status' => false, 'message' => $e->getMessage()]);
    }
}

downloadMembersPayTemplate();

?>