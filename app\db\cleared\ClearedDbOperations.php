<?php

require_once(__DIR__ . "/../../models/ClearedData.php");
require_once(__DIR__ . "/../../controllers/Cast.php");
require_once(__DIR__ . "/../../controllers/__array_key_concat.php");


class ClearedDbOperations
{

    private ?PDO $connection;

    function __construct($connection)
    {
        $this->connection = $connection;
    }

    public function get_data(): array
    {
        $query = "select * from public.harvest_cleared_record where update_flag = 0";

        $stmt = $this->connection->prepare($query);

        $stmt->execute();

        $result = array();
        while ($data = $stmt->fetchObject('ClearedData')) {
            $result[$data->getWaybillId()] = $data;
        }
        return $result;
    }

    public function get_data_all($harvest_members): array
    {
        $result = array();

        if (count($harvest_members) > 0){
            $member_ids  = array_map(array($this, "getUniqueMemberId"), $harvest_members);
            $member_ids_str = join( ",", $member_ids);

            // $query = "select * from public.harvest_cleared_record where unique_member_id in (". $member_ids_str .") and (lower(product_type) not like 'so%' and lower(variety) not like 'cash%')";
            $query = "select * from public.harvest_cleared_record where unique_member_id in (". $member_ids_str .") and (lower(product_type) not like 'so%')";

            $stmt = $this->connection->prepare($query);

            $stmt->execute();

            while ($data = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $result[$data["waybill_id"]] = $data;
            }
        }

        return $result;
    }

    // public function update_data(ClearedData $clearedData): bool
    // {
    //     $query = "UPDATE public.harvest_cleared_record SET prorated_total_weight = {$clearedData->getProratedTotalweight()}, net_weight = {$clearedData->getNetWeight()},
    //                                      average_weight = {$clearedData->getAverageWeight()}, empty_bag_weight = {$clearedData->getEmptyBagweight()},
    //                                      transporter_cost = {$clearedData->getTransporterCost()}, threshing_cost = {$clearedData->getThreshingCost()},
    //                                      cc_processing_cost = {$clearedData->getCcProcessingcost()}, costs = {$clearedData->getCosts()},
    //                                      update_flag = 1, transferred_flag = 0, updated_at = NOW() WHERE hsf_id = '{$clearedData->getHsfId()}'";

    //     $stmt = $this->connection->prepare($query);

    //     $stmt->execute();

    //     return true;
    // }

    // public function update_records($records): bool
    // {
    //     $this->insertMany($records, 1000);

    //     $records_hsfs = array_map(array($this, 'getWaybillId'), $records );
    //     $hsfs = join(",", $records_hsfs);

    //     $query = "UPDATE public.harvest_cleared_record SET update_flag = 1 WHERE hsf_id in ( {$hsfs} )";

    //     $stmt = $this->connection->prepare($query);

    //     $stmt->execute();

    //     return true;
    // }

    public function update_records($records): bool
    {
        $this->insertMany($records, 1000);

        $records_waybills = array_map(array($this, 'getWaybillId'), $records );
        $waybills = join(",", $records_waybills);

        $query = "UPDATE public.harvest_cleared_record SET update_flag = 1 WHERE waybill_id in ( {$waybills} )";

        $stmt = $this->connection->prepare($query);

        $stmt->execute();

        return true;
    }

    // public function flag_records_to_verifier($records, $chunk_size): bool
    // {
    //     $hsfIds = array_map(array($this, "getHsfId"), $records);
    //     try {
    //         $chunks = array_chunk($hsfIds, $chunk_size);
    //         foreach ($chunks as $eachChunk) {
    //             $hsfs = join(",", $eachChunk);
    //             $query = "UPDATE public.harvest_checking_record set verifier_flag = 1 WHERE hsf_id in ({$hsfs})";
    //             // echo $query;
    //             $stmt = $this->connection->prepare($query);
    //             $stmt->execute();
    //         }
    //     } catch (Exception $err) {
    //         throw new Exception("Ooops!!! Looks like we couldn't delete all cleared records - " . $err->getMessage());
    //     }
    //     return true;
    // }

    public function flag_records_to_verifier($records, $chunk_size): bool
    {
        $waybillIds = array_map(array($this, "getWaybillId"), $records);
        try {
            $chunks = array_chunk($waybillIds, $chunk_size);
            foreach ($chunks as $eachChunk) {
                $waybills = join(",", $eachChunk);
                $query = "UPDATE public.harvest_scaling_record set verifier_flag = 1 WHERE scaling_waybill_id in ({$waybills})";
                $stmt = $this->connection->prepare($query);
                $stmt->execute();
            }
        } catch (Exception $err) {
            throw new Exception("Ooops!!! Looks like we couldn't delete all cleared records - " . $err->getMessage());
        }
        return true;
    }

    // public function delete_records($records, $chunk_size): bool
    // {
    //     $hsfIds = array_map(array($this, "getHsfId"), $records);

    //     try {
    //         $chunks = array_chunk($hsfIds, $chunk_size);
    //         foreach ($chunks as $eachChunk) {
    //             $hsfs = join(",", $eachChunk);
    //             $query = "DELETE FROM public.harvest_cleared_record WHERE hsf_id in ({$hsfs})";
    //             // echo $query;
    //             $stmt = $this->connection->prepare($query);
    //             $stmt->execute();
    //         }
    //     } catch (Exception $err) {
    //         throw new Exception("Ooops!!! Looks like we couldn't delete all cleared records - " . $err->getMessage());
    //     }
    //     return true;
    // }

    public function delete_records($records, $chunk_size): bool
    {
        $waybillIds = array_map(array($this, "getWaybillId"), $records);

        try {
            $chunks = array_chunk($waybillIds, $chunk_size);
            foreach ($chunks as $eachChunk) {
                $waybills = join(",", $eachChunk);
                $query = "DELETE FROM public.harvest_cleared_record WHERE waybill_id in ({$waybills})";
                // echo $query;
                $stmt = $this->connection->prepare($query);
                $stmt->execute();
            }
        } catch (Exception $err) {
            throw new Exception("Ooops!!! Looks like we couldn't delete all cleared records - " . $err->getMessage());
        }
        return true;
    }

    public function set_transfer_flag_1()
    {
        $query = "UPDATE public.harvest_cleared_record SET transferred_flag = 1 WHERE transferred_flag = 0 and lower(product_type) not like 'so%'";

        $stmt = $this->connection->prepare($query);

        $stmt->execute();

        return true;
    }

    public function get_soy_data()
    {
        $query = "select * from public.harvest_cleared_record where lower(product_type)  like 'so%'";

        $stmt = $this->connection->prepare($query);

        $stmt->execute();

        $result = array();

        while ($data = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $result[get_key_concat($data["unique_member_id"], $data["variety"])][] = $data;
        }

        return $result;
    }

    private function getWaybillId(ClearedData $record)
    {
        $waybill_id = $record->getWaybillId();
        return "'$waybill_id'";
    }

    private function getUniqueMemberId($record)
    {
        $unique_member_id = $record["unique_member_id"];
        return "'$unique_member_id'";
    }

    // public function insertMany($insertArray, $length){
    //     $cleared_record_cols_arr = [
    //         "hsf_id","collection_center_id","hub_id", "hub_name","new_hsf","verifier_id","unique_member_id","ik_number","empty_bag_weight",
    //         "total_weight","prorated_total_weight","net_weight","product_type","variety","thresher_id","threshing_date",
    //         "threshing_cost","transporter_id","transporter_cost","cc_processing_cost","costs","bags_marketed",
    //         "moldy_grains_count_flag","verifier_flag","verifier_comment", "voucher_edit_comment","cleared_flag","created_at",
    //         "updated_at","transaction_date","moisture_percentage", "cleanliness_percentage", "moldy_grains_count"
    //         ,"average_weight","imei","app_version","update_flag"
    //     ];
    //     $duplicate_string = generateDuplicateStringPG($cleared_record_cols_arr, ['hsf_id']);


    //     $cleared_record_cols_str = join(",", $cleared_record_cols_arr);

    //     try{
    //         $chunks = array_chunk($insertArray, $length );
    //         foreach($chunks as $eachChunk){
    //             $placeholder_array = [];
    //             for($i=0; $i < count($eachChunk); $i++){
    //                 $placeholder_array[] = "(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
    //             }
    //             $placeholder_string = join(",", $placeholder_array);
    //             $query = "INSERT INTO public.harvest_cleared_record ({$cleared_record_cols_str}) VALUES {$placeholder_string} ON CONFLICT(hsf_id) DO UPDATE SET {$duplicate_string}";
    //             $stmt = $this->connection->prepare($query);

    //             $oneMultiInsertArray = [];
    //             foreach($eachChunk as $eachRecord){
    //                 $eachRecord = castClearedData($eachRecord);
    //                 $oneMultiInsertArray[] = $eachRecord->getHsfId(); //  hsf_id
    //                 $oneMultiInsertArray[] = $eachRecord->getCollectionCenterid(); // collection_center_id
    //                 $oneMultiInsertArray[] = $eachRecord->getHubId(); // hub id
    //                 $oneMultiInsertArray[] = $eachRecord->getHubName(); // hub name
    //                 $oneMultiInsertArray[] = 1; // new_hsf
    //                 $oneMultiInsertArray[] = $eachRecord->getVerifierId();; // verifier_id
    //                 $oneMultiInsertArray[] = $eachRecord->getUniqueMemberid(); // unique_member_id
    //                 $oneMultiInsertArray[] = $eachRecord->getIkNumber(); //ik_number
    //                 $oneMultiInsertArray[] = $eachRecord->getEmptyBagweight(); // empty_bag_weight
    //                 $oneMultiInsertArray[] = $eachRecord->getTotalWeight(); // total_weight
    //                 $oneMultiInsertArray[] = $eachRecord->getProratedTotalweight(); // protated_weight
    //                 $oneMultiInsertArray[] = $eachRecord->getNetWeight();// net weight
    //                 $oneMultiInsertArray[] = $eachRecord->getProductType(); // product_type
    //                 $oneMultiInsertArray[] = $eachRecord->getVariety(); // variety
    //                 $oneMultiInsertArray[] = $eachRecord->getThresherId();
    //                 $oneMultiInsertArray[] = $eachRecord->getThreshingDate();
    //                 $oneMultiInsertArray[] = $eachRecord->getThreshingCost(); // threshing_cost
    //                 $oneMultiInsertArray[] = $eachRecord->getTransporterId(); // $transporter_id;
    //                 $oneMultiInsertArray[] = $eachRecord->getTransporterCost(); // $transporter_cost;
    //                 $oneMultiInsertArray[] = $eachRecord->getCcProcessingcost(); // $cc_processing_cost;
    //                 $oneMultiInsertArray[] = $eachRecord->getCosts(); // costs;
    //                 $oneMultiInsertArray[] = $eachRecord->getBagsMarketed(); // bags_marketed
    //                 $oneMultiInsertArray[] = $eachRecord->getMoldyGrainscountflag(); // moldy_grains_count_flag
    //                 $oneMultiInsertArray[] = $eachRecord->getVerifierFlag(); // verifier_flag
    //                 $oneMultiInsertArray[] = $eachRecord->getVerifierComment(); // verifier_comment
    //                 $oneMultiInsertArray[] = $eachRecord->getVoucherEditcomment(); // voucher_edit_comment;
    //                 $oneMultiInsertArray[] = $eachRecord->getClearedFlag(); // cleared_flag;
    //                 $oneMultiInsertArray[] = $eachRecord->getCreatedAt(); // created_at;
    //                 $oneMultiInsertArray[] = date("Y-m-d H:i:s"); // updated_at;
    //                 $oneMultiInsertArray[] = $eachRecord->getTransactionDate(); // transaction_date;
    //                 $oneMultiInsertArray[] = $eachRecord->getMoisturePercentage(); // moisture_percentage
    //                 $oneMultiInsertArray[] = $eachRecord->getCleanlinessPercentage(); // cleanliness_percentage
    //                 $oneMultiInsertArray[] = $eachRecord->getMoldyGrainscount(); // moldy_grains_count;
    //                 $oneMultiInsertArray[] = $eachRecord->getAverageWeight(); // average_weight;
    //                 $oneMultiInsertArray[] = $eachRecord->getImei(); // imei;
    //                 $oneMultiInsertArray[] = $eachRecord->getAppVersion(); // app_version;
    //                 $oneMultiInsertArray[] = $eachRecord->getUpdateFlag(); // update_flag;
    //             }
    //             $stmt->execute($oneMultiInsertArray);
    //         }
    //     }
    //     catch(Exception $err){
    //         throw new Exception("Ooops!!! Looks like we couldn't insert all cleared records - ". $err->getMessage());
    //     }
    // }

    public function insertMany($insertArray, $length){
        $cleared_record_cols_arr = [
            "waybill_id","collection_center_id","hub_id", "hub_name","new_hsf","verifier_id","unique_member_id","ik_number","empty_bag_weight",
            "total_weight","prorated_total_weight","net_weight","product_type","variety","thresher_id","threshing_date",
            "threshing_cost","transporter_id","transporter_cost","cc_processing_cost","costs","bags_marketed",
            "moldy_grains_count_flag","verifier_flag","verifier_comment", "voucher_edit_comment","cleared_flag","created_at",
            "updated_at","transaction_date","moisture_percentage", "cleanliness_percentage", "moldy_grains_count"
            ,"average_weight","imei","app_version","update_flag"
        ];
        $duplicate_string = generateDuplicateStringPG($cleared_record_cols_arr, ['waybill_id']);


        $cleared_record_cols_str = join(",", $cleared_record_cols_arr);

        try{
            $chunks = array_chunk($insertArray, $length );
            foreach($chunks as $eachChunk){
                $placeholder_array = [];
                for($i=0; $i < count($eachChunk); $i++){
                    $placeholder_array[] = "(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
                }
                $placeholder_string = join(",", $placeholder_array);
                $query = "INSERT INTO public.harvest_cleared_record ({$cleared_record_cols_str}) VALUES {$placeholder_string} ON CONFLICT(waybill_id) DO UPDATE SET {$duplicate_string}";
                $stmt = $this->connection->prepare($query);

                $oneMultiInsertArray = [];
                foreach($eachChunk as $eachRecord){
                    $eachRecord = castClearedData($eachRecord);
                    $oneMultiInsertArray[] = $eachRecord->getWaybillId(); //  hsf_id
                    $oneMultiInsertArray[] = $eachRecord->getCollectionCenterid(); // collection_center_id
                    $oneMultiInsertArray[] = $eachRecord->getHubId(); // hub id
                    $oneMultiInsertArray[] = $eachRecord->getHubName(); // hub name
                    $oneMultiInsertArray[] = 1; // new_hsf
                    $oneMultiInsertArray[] = $eachRecord->getVerifierId();; // verifier_id
                    $oneMultiInsertArray[] = $eachRecord->getUniqueMemberid(); // unique_member_id
                    $oneMultiInsertArray[] = $eachRecord->getIkNumber(); //ik_number
                    $oneMultiInsertArray[] = $eachRecord->getEmptyBagweight(); // empty_bag_weight
                    $oneMultiInsertArray[] = $eachRecord->getTotalWeight(); // total_weight
                    $oneMultiInsertArray[] = $eachRecord->getProratedTotalweight(); // protated_weight
                    $oneMultiInsertArray[] = $eachRecord->getNetWeight();// net weight
                    $oneMultiInsertArray[] = $eachRecord->getProductType(); // product_type
                    $oneMultiInsertArray[] = $eachRecord->getVariety(); // variety
                    $oneMultiInsertArray[] = $eachRecord->getThresherId();
                    $oneMultiInsertArray[] = $eachRecord->getThreshingDate();
                    $oneMultiInsertArray[] = $eachRecord->getThreshingCost(); // threshing_cost
                    $oneMultiInsertArray[] = $eachRecord->getTransporterId(); // $transporter_id;
                    $oneMultiInsertArray[] = $eachRecord->getTransporterCost(); // $transporter_cost;
                    $oneMultiInsertArray[] = $eachRecord->getCcProcessingcost(); // $cc_processing_cost;
                    $oneMultiInsertArray[] = $eachRecord->getCosts(); // costs;
                    $oneMultiInsertArray[] = $eachRecord->getBagsMarketed(); // bags_marketed
                    $oneMultiInsertArray[] = $eachRecord->getMoldyGrainscountflag(); // moldy_grains_count_flag
                    $oneMultiInsertArray[] = $eachRecord->getVerifierFlag(); // verifier_flag
                    $oneMultiInsertArray[] = $eachRecord->getVerifierComment(); // verifier_comment
                    $oneMultiInsertArray[] = $eachRecord->getVoucherEditcomment(); // voucher_edit_comment;
                    $oneMultiInsertArray[] = $eachRecord->getClearedFlag(); // cleared_flag;
                    $oneMultiInsertArray[] = $eachRecord->getCreatedAt(); // created_at;
                    $oneMultiInsertArray[] = date("Y-m-d H:i:s"); // updated_at;
                    $oneMultiInsertArray[] = $eachRecord->getTransactionDate(); // transaction_date;
                    $oneMultiInsertArray[] = $eachRecord->getMoisturePercentage(); // moisture_percentage
                    $oneMultiInsertArray[] = $eachRecord->getCleanlinessPercentage(); // cleanliness_percentage
                    $oneMultiInsertArray[] = $eachRecord->getMoldyGrainscount(); // moldy_grains_count;
                    $oneMultiInsertArray[] = $eachRecord->getAverageWeight(); // average_weight;
                    $oneMultiInsertArray[] = $eachRecord->getImei(); // imei;
                    $oneMultiInsertArray[] = $eachRecord->getAppVersion(); // app_version;
                    $oneMultiInsertArray[] = $eachRecord->getUpdateFlag(); // update_flag;
                }
                $stmt->execute($oneMultiInsertArray);
            }
        }
        catch(Exception $err){
            throw new Exception("Ooops!!! Looks like we couldn't insert all cleared records - ". $err->getMessage());
        }
    }
    public function updateCashLocal(): bool
    {
        $query = "UPDATE harvest_cleared_record SET product_type = 'Cash Local' WHERE variety ILIKE '%Cash%' AND product_type <> 'Cash Local'";

        $stmt = $this->connection->prepare($query);

        $stmt->execute();

        return true;
    }

    public function selectCashLocal()
    {
        $query = "SELECT * FROM (SELECT unique_member_id, MAX(ik_number) AS ik_number, product_type AS product_type, SUM(net_weight::numeric) AS net_weight, SUM(threshing_cost::numeric) AS threshing_cost,
        SUM(transporter_cost::numeric) AS transport_cost, SUM(cc_processing_cost::numeric) AS processing_cost,SUM(costs::numeric) AS total_cost, SUM(bags_marketed) AS bags_marketed
        FROM harvest_cleared_record GROUP BY unique_member_id,product_type) cash WHERE product_type = 'Cash Local'
        AND unique_member_id IN (SELECT DISTINCT unique_member_id FROM harvest_cleared_record WHERE product_type = 'Cash Local' AND transferred_flag = 0 )";

        $stmt = $this->connection->prepare($query);

        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

}