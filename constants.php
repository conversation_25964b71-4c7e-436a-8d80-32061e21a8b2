<?php

define('DB_HOST', '************');
define('DB_USER', 'bulkbana_mkt');
define('DB_PASSWORD', 'Se//ect2019$$');
define('DB_NAME', 'bulkbana_mkt');

define('PG_DB_HOST' , '*************');
define('PG_DB_USER' , 'agricos-prod');
define('PG_DB_PASSWORD' , 'Ysi7juc4TXF4rHk');
define('PG_INVENTORY_DB_NAME' , 'inventory-db');
define('PG_INVENTORY_BACKUP_DB_NAME' , 'inventory-db-backup-2023');
define('PG_FINANCE_DB_NAME' , 'financial_transactions');
define('PG_FARMING_DB_NAME' , 'farming-db');
define('PG_PORTFOLIO_DB_NAME' , 'portfolio-db');
define('PG_PLANNING_DB_NAME' , 'planning-db');
define('PG_BG_SIGNON_DB_NAME' , 'bg_signon');
define('PG_TRANSPORTATION_DB_NAME' , 'transportation-db');
define('PG_RECRUITMENT_DB_NAME' , 'recruitment-db');
define('PAYSTACK_BASE_URL' , 'https://api.paystack.co');
define('APP_SECRET' , 'babbangona is wonderful');

define('PAY_STATUS' ,
 [
    "Zero harvest advance AND UNPAID" => -2,
    "UNDERPAID" => -1,
    "UNPAID" => 0,
    "PAID" => 1,
    "OVERPAID" => 2
 ]);

define('MAX_MEMBER_PAYOUT', 1600000);

define('MAX_FLOAT_PAYOUT', 300000);
define('PER_PAGE', 1000);

define('VALID_CLASSIFICATIONS', [
   "Member HA Repayment",
   "Emergency Labour Float",
   "Emergency Transportation Float",
   "Future Bonus",
   "Operator Profit Share",
   "Upfront Refund",
   "TGE Bonus",
   "ID Incentive",
   "PWS Payout",
   "WEDI LMR Payment",
   "WEDI Payment",
]);

define('DNP_CATEGORY', [
   "Finance DNP",
   "Tech DNP",
])
?>
