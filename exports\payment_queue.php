<?php
// require_once(__DIR__ . '/../api/auth.php');
// require_once(__DIR__ . '/../app/controllers/cors.php');
// require_once(__DIR__ . '/../constants.php');
// require_once(__DIR__ . '/../connect_db.php');
// require_once(__DIR__ . '/../app/models/app_logs.php');

// cors();

// function downloadPaymentQueue($conn)
// {
//     try {
//         $user = validateUser();
//         if (!$user) {
//             throw new CustomException(401, "Invalid Request. User could not be authenticated");
//         }

//         $driver = new DBDriver;
//         $conn = $driver->connectPgSql(PG_FINANCE_DB_NAME);

//         // Retrieve the 'filter' value from request parameters
//         $filter = isset($_GET['filter']) ? $_GET['filter'] : '';

//         $columns = ['action', 'reference', 'payee_id', 'payment_category', 'amount', 'payee_recipient_id', 'account_number', 'bank_name'];

//         date_default_timezone_set('Africa/Lagos');
//         $dates = date("Y-m-d H:i:s"); // Year-Month-Day Hour:Minute:Second
//         $tableName = 'payment_queue';
//         $filename = $tableName . "-" . $dates;
//         header('Content-Type: text/csv');
//         header("Content-Disposition: attachment;filename=\"{$filename}.csv\"");

//         $fp = fopen('php://output', 'w');

//         fputcsv($fp, $columns);

//         $sql = "SELECT " . implode(", ", $columns) . " FROM payment_queue";
//         if ($filter === 'Company Transporter') {
//             $sql .= " WHERE action LIKE '%Company Transporter%'";
//         } elseif ($filter === 'Labourer') {
//             $sql .= " WHERE action LIKE '%Labourer%'";
//         } elseif ($filter === 'Independent Transporter') {
//             $sql .= " WHERE action LIKE '%Independent Transporter%'";
//         } elseif ($filter === 'Emergency Labour Float') {
//             $sql .= " WHERE action LIKE '%Emergency Labour Float%'";
//         } elseif ($filter === 'Emergency Transportation Float') {
//             $sql .= " WHERE action LIKE '%Emergency Transportation Float%'";
//         } elseif ($filter === 'Future Bonus') {
//             $sql .= " WHERE action LIKE '%Future Bonus%'";
//         }

//         $stmt = $conn->prepare($sql);

//         if ($stmt->execute()) {
//             while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
//                 fputcsv($fp, $row);
//             }
//         }
//         fclose($fp);

//         // Logging the user's action
//         AppLogs::insertOne($conn, generateLogs($user->data->staff_id, "USER DOWNLOADED ALL PAYMENT QUEUED", ""));
//     } catch (CustomException $e) {
//         http_response_code($e->status ?? 500);
//         echo json_encode(['status' => false, 'message' => $e->getMessage()]);
//     } catch (Exception $e) {
//         http_response_code(500);
//         echo json_encode(['status' => false, 'message' => $e->getMessage()]);
//     }
// }


// downloadPaymentQueue($conn);

?>
