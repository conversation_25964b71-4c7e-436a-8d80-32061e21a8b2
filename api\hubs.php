<?php
require_once '../connect_db.php';
require_once '../constants.php';
require_once '../app/controllers/cors.php';
require_once '../app/models/exception.php';
require_once('auth.php');
require_once("../app/controllers/__data_helpers.php");

cors();

if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    
    $user = validateUser();
    $json = file_get_contents('php://input');
    $data = json_decode($json, true);
    try {
        $driver = new DBDriver;
        $conn = $driver->connectPgSql(PG_PLANNING_DB_NAME);

        $result = getTGHubs($conn);

        if ($result) {
            $code = 200;
            $response = get_response(true, "Data fetched successfully", $result, null);
        } else {
            $code = 404;
            $response = get_response(false, "No data found", null, "No records found.");
        }
    } catch (CustomException $exception) {
        $response = get_response(false, "An error occurred while fetching the data", null, $exception->getMessage());
        $code = 400;
    }
} else {
    $response = get_response(false, "Request Method Not Allowed", null, "Only GET method is allowed.");
    $code = 403;
}

http_response_code($code);
echo json_encode($response);

function get_response($status, $message, $data = null, $error = null)
{
    return [
        'success' => $status,
        'message' => $message,
        'data' => $data,
        'error' => $error
    ];
}
