<?php

class PaymentSecrets
{
        /* selectOne : Runs a select query on payment_secrets table selecting all secrets
         * @param $con : database conection object
         * returns - null
        */
    public static function selectOne($con,$key){
        try{
            $stmt = $con->prepare("SELECT * FROM payment_secrets WHERE key ILIKE ?");
            $stmt->execute(["%{$key}%"]);
            $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
            }
            catch(Exception $err){
                throw new Exception("Ooops!!! Looks like we couldn't fetch a payment reference ". $err->getMessage());
            }
    }

    public static function selectMany($con){
        try{
            $stmt = $con->prepare("SELECT key,value FROM payment_secrets");
            $stmt->execute();
            $secrets = new stdClass();
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC))
            {
                $secrets->{$row['key']} = $row['value'];

            }
            return $secrets;
            }
            catch(Exception $err){
                throw new Exception("Ooops!!! Looks like we couldn't fetch payment secrets ". $err->getMessage());
            }
    }
    
    public static function updateOne($con, $id, $update_pairs)
    {
        $update_string = '';
        try {
            // build up the update string
            foreach ($update_pairs as $key => $value) {
                $update_string .= "{$key} ='{$value}',";
            }
            $stmt = $con->prepare("UPDATE payment_secrets SET {$update_string} updated_at = NOW() WHERE key = ?");
            $stmt->execute([$id]);
        } catch (Exception $err) {
            throw new Exception("Ooops!!! Looks like we couldn't UPDATE payment secret - " . $err->getMessage());
        }
    }

    public static function insertOne($con, $data)
    {
        try {
            $stmt = $con->prepare("INSERT INTO payment_secrets (key,value,description) VALUES(?,?,?)");
            $stmt->execute([$data['key'],$data['value'],$data['description'] ?? null]);
        } catch (Exception $err) {
            throw new Exception("Ooops!!! Looks like we couldn't INSERT payment secret - " . $err->getMessage());
        }
    }
}

?>