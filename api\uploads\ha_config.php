<?php
require_once(__DIR__ . '/../../constants.php');
require_once(__DIR__ . '/../../connect_db.php');
require_once(__DIR__ . '/../../api/auth.php');
require_once(__DIR__ . '/../../app/models/app_logs.php');
require_once(__DIR__ . '/../../app/models/exception.php');
require_once(__DIR__ . '/../../app/controllers/cors.php');
require_once(__DIR__ . '/../../app/models/app_logs.php');

cors();

function getDbConnection()
{
    $driver = new DBDriver;
    return $driver->connectPgSql(PG_FINANCE_DB_NAME);
}

function uploadHAConfig($handle, $conn)
{
    $stmt = $conn->prepare(
        "INSERT INTO cc_hub_to_grain_price (hub, grain, price) 
        VALUES (:hub, :grain, :price) 
        ON CONFLICT (hub, grain) 
        DO UPDATE SET 
            price = EXCLUDED.price"
    );

    rewind($handle);
    // Skip the header row
    fgetcsv($handle);

    while (($data = fgetcsv($handle)) !== FALSE) {
        
        $params = [
            ':hub' => $data[0],
            ':grain' => $data[1],
            ':price' => $data[2],
        ];

        $stmt->execute($params);
    }
    fclose($handle);
}



function main()
{
    try {
        $user = validateUser();

        if ($_SERVER['REQUEST_METHOD'] == 'GET' && isset($_GET['checkUploadStatus']) && $_GET['checkUploadStatus'] === 'true') {
            checkUploadStatus();
            return;
        }

        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            throw new CustomException(403, "Request Method Not Allowed");
        }

        $fileArray = $_FILES['file'] ?? [];
        if (empty($fileArray)) {
            throw new CustomException(400, "Please upload a valid CSV file for payment");
        }

        $fileExt = strtolower(pathinfo($fileArray['name'], PATHINFO_EXTENSION));
        if ($fileExt !== 'csv') {
            throw new CustomException(400, "Please upload a valid CSV file for payment");
        }

        $fileContent = $fileArray['tmp_name'];
        $conn = getDbConnection();

        if (($handle = fopen($fileContent, "r")) !== FALSE) {
            // Check for column headers
            $expectedColumns = ['hub', 'grain', 'price'];
            $csvColumns = fgetcsv($handle);
            $csvColumns = array_map('trim', $csvColumns);
            if ($csvColumns !== $expectedColumns) {
                throw new CustomException(400, "CSV columns do not match the expected columns");
            }

            // Check for an empty CSV file
            $dataRow = fgetcsv($handle);
            if (feof($handle) || $dataRow === false) {
                throw new CustomException(400, "The CSV file is empty");
            }

            uploadHAConfig($handle, $conn);
            $response = setResponse(true, "Data uploaded successfully", null, null);
            http_response_code(200);
            echo json_encode($response);
            AppLogs::insertOne($conn, generateLogs($user->data->staff_id, "USER UPLOADED HA CONFIG", $fileArray['name']));
        }
    } catch (CustomException $e) {
        http_response_code($e->status ?? 500);
        echo json_encode(setResponse(false, "Something went wrong..... ", [], $e->getMessage()));
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(setResponse(false, "An unexpected error occurred", [], $e->getMessage()));
    }
}

function checkUploadStatus()
{
    try {
        $conn = getDbConnection();
        $stmt = $conn->prepare("SELECT COUNT(*) FROM cc_hub_to_grain_price");
        $stmt->execute();
        $count = $stmt->fetchColumn();

        http_response_code(200);
        echo json_encode(['status' => true, 'data' => ['tableEmpty' => $count == 0]]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['status' => false, 'message' => $e->getMessage()]);
    }
}

main();

?>