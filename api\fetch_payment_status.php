<?php
require_once '../connect_db.php';
require_once '../constants.php';
require_once '../app/controllers/cors.php';
require_once '../app/models/exception.php';
require_once('auth.php');

cors();

$user = validateUser();
$json = file_get_contents('php://input');
$data = json_decode($json, true);

$reference_id = $_GET['reference_id'];

if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        $driver = new DBDriver;
        $conn_finance = $driver->connectPgSql(PG_FINANCE_DB_NAME);

        $sql = "SELECT a.reference_id, a.payee_id, a.initiator_name, a.initiator_role, a.initiated_at, a.amount, a.approver_name, a.approver_role, a.approved_at, a.status, a.created_at, a.updated_at, b.extra_info FROM payment_tracker a JOIN payment_queue b ON a.reference_id = b.reference WHERE a.reference_id = ?";

        $stmt = $conn_finance->prepare($sql);
        $stmt->execute([$reference_id]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($result) {
            $code = 200;
            $response = get_response(true, "Data fetched successfully", $result, null);
        } else {
            $code = 404;
            $response = get_response(false, "No data found for the given reference_id", null, "No records found.");
        }
    } catch (CustomException $exception) {
        $response = get_response(false, "An error occurred while fetching the data", null, $exception->getMessage());
        $code = 400;
    }
} else {
    $response = get_response(false, "Request Method Not Allowed", null, "Only GET method is allowed.");
    $code = 403;
}

http_response_code($code);
echo json_encode($response);

function get_response($status, $message, $data = null, $error = null)
{
    return [
        'success' => $status,
        'message' => $message,
        'data' => $data,
        'error' => $error
    ];
}
