<?php
require_once '../connect_db.php';
require_once '../constants.php';
require_once '../app/controllers/cors.php';
require_once(__DIR__ . '/../app/models/app_logs.php');
require_once (__DIR__.'/../app/models/exception.php');
require_once ('auth.php');

cors();

$user = validateUser();

$json = file_get_contents('php://input');
$data = json_decode($json, true);

$type = $data["type"] ?? "";
$product_type = $data["product_type"] ?? "";
$hub_classification = $data["hub_classification"] ?? "";
$transport_cost_per_bag = $data["transport_cost_per_bag"] ?? 0;
$laborer_cost_per_bag = $data["laborer_cost_per_bag"] ?? 0;
$pay_until = strtoupper($data["pay_until"] ?? "");
$repeats_every = $data["repeats_every"] ?? array();
$time_of_payment = $data["time_of_payment"] ?? "";
$variety = $data["variety"] ?? "";
$processing_cost_per_bag = $data["processing_cost_per_bag"] ?? 0;
$threshing_cost_per_ha = $data["threshing_cost_per_ha"] ?? 0;
$price_per_kg = $data["price_per_kg"] ?? 0;
$similar_varieties = $data["similar_varieties"] ?? "";

$valid_days = ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"];
$diff = array_diff($repeats_every, $valid_days);

if (!in_array($type, ["float_payment", "tg_harvest_payment", "aggregate_payment"])) {
    $response = get_response(false, "Please provide a valid config type", null);
    $code = 400;
} elseif (!isset($data["repeats_every"]) && !isset($data["pay_until"]) && !isset($data["time_of_payment"])) {
    $response = get_response(false, "Please provide all valid fields for the selected type.", null);
    $code = 400;
} elseif (count($diff) > 0) {
    $response = get_response(false, "Please provide a valid repeat days: " . join(",", $valid_days), null);
    $code = 400;
} elseif ($type == "float_payment" && (
        !isset($data["hub_classification"]) ||
        !isset($data["transport_cost_per_bag"]) ||
        !isset($data["laborer_cost_per_bag"]) ||
        !isset($data["time_of_payment"]) ||
        !isset($data["repeats_every"]))) {
    $response = get_response(false, "Please provide all valid fields for the selected type.", null);
    $code = 400;
} elseif ($type == "tg_harvest_payment" && (
        !isset($data["product_type"]) ||
        !isset($data["variety"]) ||
        !isset($data["transport_cost_per_bag"]) ||
        !isset($data["processing_cost_per_bag"]) ||
        !isset($data["threshing_cost_per_ha"]) ||
        !isset($data["price_per_kg"]) ||
        !isset($data["similar_varieties"])
    )) {
    $response = get_response(false, "Please provide all valid fields for the selected type.", null);
    $code = 400;
} elseif ($type == "aggregate_payment" && (
        !isset($data["product_type"]) ||
        !isset($data["variety"]) ||
        !isset($data["price_per_kg"])
    )) {
    $response = get_response(false, "Please provide all valid fields for the selected type.", null);
    $code = 400;
} else {
    try {
        $user = validateUser();
        if(!$user) throw new CustomException(401, "Invalid Request. User could not be authenticated");

        $driver = new DBDriver;
        $conn_inventory = $driver->connectPgSql(PG_FINANCE_DB_NAME);

        $sql = "INSERT into payment_portal_config(type, product_type,hub_classification,transport_cost_per_bag,laborer_cost_per_bag,pay_until,repeats_every,time_of_payment,variety,processing_cost_per_bag,threshing_cost_per_ha,price_per_kg,similar_varieties)
 VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)";

        $stmt = $conn_inventory->prepare($sql);

        $repeats_every_str = join(",", $repeats_every);

        $result = $stmt->execute([$type, $product_type, $hub_classification, $transport_cost_per_bag, $laborer_cost_per_bag, $pay_until, $repeats_every_str, $time_of_payment, $variety, $processing_cost_per_bag, $threshing_cost_per_ha, $price_per_kg, $similar_varieties]);

        if ($result) {
            $response = get_response(true, "Configuration has been successfully added", null);
            $code = 200;
            AppLogs::insertOne($conn_inventory, generateLogs($user->user_name, "USER CREATED CONFIG", $data));
        } else {
            $response = get_response(false, "Unable to add configuration", $stmt->errorInfo());
            $code = 400;
        }
    } catch (Exception $exception) {
        $response = get_response(false, "Unable to add configuration", $exception->getMessage());
        $code = $exception->status ?? 500;
    }

}


http_response_code($code);
echo json_encode($response);

function get_response($status, $message, $error)
{
    return [
        'success' => $status,
        'message' => $message,
        'error' => $error
    ];
}

?>