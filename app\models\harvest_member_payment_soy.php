<?php

require_once(__DIR__ . "/../../app/controllers/__array_key_concat.php");
require_once(__DIR__ . "/../../constants.php");
require_once(__DIR__ . "/../controllers/__data_helpers.php");


class HarvestMemberPaymentSoy
{

    private ?PDO $connection;

    function __construct($connection)
    {
        $this->connection = $connection;
    }


    public function insert_many($records, $chunk_size): bool
    {
        $records_cols_arr = [
            "unique_member_id", "ik_number", "variety", "season", "field_size", "member_status", "percentage_ownership",
            "no_of_bags_marketed", "net_weight_marketed", "grain_value",
            "harvest_advance", "loan_before_harvest", "net_harvest_advance",
            "misc_account", "threshing_cost", "transport_cost", "processing_cost", "total_cost", "shared_debt",
            "payment_ready_date", "updated_flag", "contractual_flag","exceeded_expectation", "created_at", "updated_at",
        ];

        $duplicate_string = generateDuplicateStringPG($records_cols_arr, ['unique_member_id', 'variety', 'season']);

        $record_cols_str = join(",", $records_cols_arr);

        try {
            $chunks = array_chunk($records, $chunk_size);
            foreach ($chunks as $eachChunk) {
                $placeholder_array = [];
                for ($i = 0; $i < count($eachChunk); $i++) {
                    $placeholder_array[] = "(" . "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?" . ")";
                }
                $placeholder_string = join(",", $placeholder_array);
                $query = "INSERT INTO public.harvest_member_payments_soy ({$record_cols_str}) VALUES {$placeholder_string} ON CONFLICT(unique_member_id,variety,season) DO UPDATE SET {$duplicate_string}";
                // echo $query;
                $stmt = $this->connection->prepare($query);

                $oneMultiInsertArray = [];

                foreach ($eachChunk as $data) {
                    $oneMultiInsertArray[] = $data["unique_member_id"];
                    $oneMultiInsertArray[] = $data["ik_number"];
                    $oneMultiInsertArray[] = $data["variety"];
                    $oneMultiInsertArray[] = $data["season"];
                    $oneMultiInsertArray[] = 0;
                    $oneMultiInsertArray[] = 1;
                    $oneMultiInsertArray[] = 0;
                    $oneMultiInsertArray[] = $data["no_of_bags_marketed"];
                    $oneMultiInsertArray[] = $data["net_weight_marketed"];
                    $oneMultiInsertArray[] = $data["grain_value"];
                    $oneMultiInsertArray[] = $data["harvest_advance"];
                    $oneMultiInsertArray[] = 0;
                    $oneMultiInsertArray[] = $data["harvest_advance"];
                    $oneMultiInsertArray[] = 0;
                    $oneMultiInsertArray[] = $data["threshing_cost"];
                    $oneMultiInsertArray[] = $data["transport_cost"];
                    $oneMultiInsertArray[] = $data["processing_cost"];
                    $oneMultiInsertArray[] = $data["total_cost"];
                    $oneMultiInsertArray[] = 0;
                    $oneMultiInsertArray[] = '';
                    $oneMultiInsertArray[] = $data["updated_flag"];
                    $oneMultiInsertArray[] = 0;
                    $oneMultiInsertArray[] = 0;
                    $oneMultiInsertArray[] = date("Y-m-d H:i:s"); // created_at;
                    $oneMultiInsertArray[] = date("Y-m-d H:i:s"); // updated_at;
                }
                $stmt->execute($oneMultiInsertArray);
            }
        } catch (Exception $err) {
            throw new Exception("Ooops!!! Looks like we couldn't insert all cleared records - " . $err->getMessage());
        }
        return true;
    }

    public function reset_deleted_data(): bool
    {
        $query = "UPDATE public.harvest_member_payments_soy SET no_of_bags_marketed = 0, net_weight_marketed = 0, threshing_cost = 0,
                                                            transport_cost = 0, processing_cost = 0, total_cost = 0, updated_flag = 0,
                                                            misc_account = 0, grain_value = 0, loan_before_harvest = 0, harvest_advance = 0,
                                                            shared_debt = 0, net_harvest_advance = 0, percentage_ownership = 0, transferred_flag = 0 WHERE
                                                            CONCAT(unique_member_id, variety) in (
                                                                SELECT CONCAT(unique_member_id, variety) FROM public.harvest_member_payments_soy WHERE CONCAT(unique_member_id, variety) not in (
                                                                    SELECT CONCAT(unique_member_id, variety) from foreign_harvest_cleared_record  
                                                                ) and grain_value > 0
                                                            )";
        $stmt = $this->connection->prepare($query);

        $stmt->execute();

        return true;
    }

}