<?php

class CustomException extends Exception
{
    public $status;
    public $message;

    function __construct($status, $message)
    {
        $this->status = $status;
        $this->message = $message;
        parent::__construct($message);
    }
}

try {
    // Some code that may throw your custom exception
    //throw new CustomException(500, "An error occurred");
} catch (CustomException $e) {
    echo "Error Message: " . $e->getMessage() . "\n";
    echo "Status: " . $e->status . "\n";
}

?>