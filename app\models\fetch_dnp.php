<?php

/**
 * Fetch DNP records from finance and portfolio databases
 * @param PDO $conn_finance Connection to the finance database
 * @param PDO $conn_portfolio Connection to the portfolio database
 * @param PDO $conn_planning Connection to the planning database
 * @param PDO $conn_bg_signon Connection to the bg signon database
 * @param int|array $id The ID(s) of the DNP records to fetch. If not provided, all records will be fetched.
 * @return array An array of DNP records, each containing the following information:
 * - id (string): The ID of the DNP record
 * - ik_number (string): The IK number of the trust group
 * - hub_id (int): The ID of the hub
 * - dnp_category (string): The category of the DNP record
 * - date_logged (string): The date the DNP record was logged
 * - date_solved (string|null): The date the DNP record was solved
 * - reason (string|null): The reason for the DNP record
 * - status (int): The status of the DNP record
 * - comment_created (string|null): The comment left when the DNP record was created
 * - comment_solved (string|null): The comment left when the DNP record was solved
 * - created_by (string): The staff ID of the user who created the DNP record
 * - resolved_by (string|null): The staff ID of the user who solved the DNP record
 * - hub_name (string|null): The name of the hub
 * - creator_name (string|null): The name of the user who created the DNP record
 * - resolver_name (string|null): The name of the user who solved the DNP record
 */
function get_DNP($conn_finance, $conn_portfolio, $conn_planning, $conn_bg_signon, $id = null)
{
   
        $sql = "SELECT dl.dnp_id as id, dl.ik_number, dl.hub_id ::integer, dl.dnp_category, dl.date_logged, dl.date_solved, dr.dnp_reason AS reason, dl.status, dl.comment_created, dl.comment_solved, dl.created_by, dl.resolved_by, 
        CASE 
	        WHEN dl.status = 0 THEN CURRENT_DATE - dl.date_logged::date
	        ELSE 0
        END AS days_since_logged
        FROM dnp_logs dl JOIN dnp_reasons dr ON dr.id = dl.dnp_reason_id";

        if ($id) {
            $cleanIds = preg_replace("/['\"]/", "", $id);
            $idArray = array_map('trim', explode(',', $cleanIds));
            $placeholders = implode(',', array_fill(0, count($idArray), '?'));
            $sql .= " WHERE dnp_id IN ($placeholders)";
        }
        
        $stmt = $conn_finance->prepare($sql);
        if ($id) {
            $stmt->execute($idArray);
        } else {
            $stmt->execute();
        }
            
        $financeResult = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $shp_sql = "SELECT rf_id as id, ik_number, hub_id ::integer, 'SHP DNP' as dnp_category, date_logged, date_solved, 'SHP Flagged Defaulter' as reason, rf_status as status, comment_created, comment_solved, staff_id_created as created_by, staff_id_solved as resolved_by, 
        CASE 
	        WHEN rf_status = 0 THEN CURRENT_DATE - date_logged::date
	        ELSE 0
        END AS days_since_logged
        FROM trust_group_generated_rf_entity WHERE red_flag_id = 'rf_0177'";

        if ($id) {
            $cleanIds = preg_replace("/['\"]/", "", $id);
            $idArray = array_map('trim', explode(',', $cleanIds));
            $placeholders = implode(',', array_fill(0, count($idArray), '?'));
            $shp_sql .= " AND rf_id IN ($placeholders)";
        }

        $stmt = $conn_portfolio->prepare($shp_sql);
        if ($id) {
            $stmt->execute($idArray);
        } else {
            $stmt->execute();
        }
    
        $portfolioResult = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $ik_num = [];
        $hub_id = [];
        $staff_id = [];
        $combinedResults = array_merge($financeResult, $portfolioResult);

        foreach ($combinedResults as $key) {
            if (isset($key['ik_number'])) {
                $ik_num[] = $key['ik_number'];
            }

            if (isset($key['hub_id'])) {
                $hub_id[] = $key['hub_id'];
            }

            if (isset($key['created_by'])) {
                $staff_id[] = $key['created_by'];
            }

            if (isset($key['resolved_by'])) {
                $staff_id[] = $key['resolved_by'];
            }
        }

        $tg_leaders = getTGLeaders($ik_num);
        $hub_names = getTGHubs($conn_planning, $hub_id);
        $staff_names = getStaffNames($conn_bg_signon, $staff_id);

        // $indexed_members = [];
        // foreach ($combinedResults as $member) {
        //     $indexed_members[$member['ik_number']] = $member;
        // }
        $indexed_members = [];
        foreach ($combinedResults as $member) {
            $ik_number = $member['ik_number'];
    
            // Check if this ik_number already exists
            if (!isset($indexed_members[$ik_number])) {
                // Initialize an array for this ik_number
                $indexed_members[$ik_number] = [];
            }
    
            // Add this member data to the array for this ik_number
            $indexed_members[$ik_number][] = $member;
        }

        $indexed_hubs = [];
        foreach ($hub_names as $hub) {
            $indexed_hubs[$hub['hub_id']] = $hub['hub_name'];
        }

        $indexed_staffs = [];
        foreach ($staff_names as $staff) {
            $indexed_staffs[$staff['id']] = $staff['name'];
        }

        $result = [];

        foreach ($tg_leaders as $leader) {
            $ikN = $leader['ik_number'];

            if (isset($indexed_members[$ikN])) {
                foreach ($indexed_members[$ikN] as $member) {
                    $mergedItem = array_merge($leader, $member);
                    $hubId = $member['hub_id'];
                    $mergedItem['hub_name'] = isset($indexed_hubs[$hubId]) ? $indexed_hubs[$hubId] : null;
                    $createdBy = isset($member['created_by']) ? $member['created_by'] : null;
                    $mergedItem['creator_name'] = ($createdBy == 'System' || $createdBy === null) ? $createdBy : (isset($indexed_staffs[$createdBy]) ? $indexed_staffs[$createdBy] : null);
                    $resolvedBy = isset($member['resolved_by']) ? $member['resolved_by'] : null;
                    $mergedItem['resolver_name'] = ($resolvedBy == 'System' || $resolvedBy === null) ? $resolvedBy : (isset($indexed_staffs[$resolvedBy]) ? $indexed_staffs[$resolvedBy] : null);

                    $result[] = $mergedItem;
                }
            }
        }


        usort($result, function($a, $b) {
            return $a['status'] - $b['status'];
            // return strtotime($b['date_logged']) - strtotime($a['date_logged']);
        });        
        return $result;
}

?>