<?php
require_once '../connect_db.php';
require_once '../constants.php';
require_once("../app/db/config/ConfigDbOperations.php");
require_once '../app/controllers/cors.php';
require_once '../app/controllers/payment_status.php';
require_once(__DIR__ . '/../app/models/exception.php');
require_once '../api/payment_helper.php';

cors();

$json = file_get_contents('php://input');
$data = json_decode($json, true);

date_default_timezone_set('Africa/Lagos');
$dates = date("Y-m-d");
$file = "aggregate_payment";//File Name
$filename = $file . "-" . $dates;

$response = get_soy_payment()["data"];


extract_payment_file($response, $filename);


?>