<?php
require_once '../../connect_db.php';
require_once '../../constants.php';
require_once '../../app/controllers/cors.php';
require_once '../../app/models/exception.php';
require_once '../../app/models/fetch_dnp.php';
require_once('../auth.php');
require_once("../../app/controllers/__data_helpers.php");

cors();

if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    
    $user = validateUser();
    $json = file_get_contents('php://input');
    $data = json_decode($json, true);
    try {
        $driver = new DBDriver;
        $conn_finance = $driver->connectPgSql(PG_PORTFOLIO_DB_NAME);

        $sql = "SELECT rf_id, ik_number, hub_id, date_logged, comment_created, rf_status FROM trust_group_generated_rf_entity WHERE red_flag_id = 'rf_0177'";

        $stmt = $conn_finance->prepare($sql);
        $stmt->execute();
        $financeResult = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $ik_num = [];
        foreach ($financeResult as $key) {
            if (isset($key['ik_number'])) {
                $ik_num[] = $key['ik_number'];
            }
        }

        $tg_leaders = getTGLeaders($ik_num);

        $indexed_members = [];
        foreach ($financeResult as $member) {
            $indexed_members[$member['ik_number']] = $member;
        }

        $result = [];
        foreach ($tg_leaders as $leader) {
            $ikN = $leader['ik_number'];
            if (isset($indexed_members[$ikN])) {
                $mergedItem = array_merge($leader, $indexed_members[$ikN]);
            $result[] = $mergedItem;
            }
        }

        if ($result) {
            $code = 200;
            $response = get_response(true, "Data fetched successfully", $result, null);
        } else {
            $code = 404;
            $response = get_response(false, "No data found", null, "No records found.");
        }
    } catch (CustomException $exception) {
        $response = get_response(false, "An error occurred while fetching the data", null, $exception->getMessage());
        $code = 400;
    }
} else {
    $response = get_response(false, "Request Method Not Allowed", null, "Only GET method is allowed.");
    $code = 403;
}

http_response_code($code);
echo json_encode($response);

function get_response($status, $message, $data = null, $error = null)
{
    return [
        'success' => $status,
        'message' => $message,
        'data' => $data,
        'error' => $error
    ];
}
