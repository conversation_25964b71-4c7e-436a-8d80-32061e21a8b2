<?php
require_once (__DIR__.'/../../constants.php');
require_once (__DIR__.'/../../connect_db.php');
require_once (__DIR__.'/../auth.php');
require_once (__DIR__.'/../../app/models/exception.php');
require_once (__DIR__.'/../../app/models/member_cards.php');
require_once (__DIR__.'/../../app/controllers/cors.php');

cors();
(function(){
try{
    validateUser();
    
    $driver = new DBDriver;
    $con = $driver->connectPgSql(PG_FINANCE_DB_NAME);


    if($_SERVER['REQUEST_METHOD'] == 'GET'){
        $member_cards = MemberCards::selectMany($con);
        $response = setResponse(true,"All member card details successfully retrieved",$member_cards,null);
        http_response_code(200);
        echo json_encode($response);
    }
    else{
        throw new CustomException(403,"Request Method Not Allowed");
}
}
catch(Exception $e){
    http_response_code($e->status ?? 500);
    echo json_encode(setResponse(false,"Something went wrong.. ", null, $e->getMessage()));
}
$con= null; 
})()
?>