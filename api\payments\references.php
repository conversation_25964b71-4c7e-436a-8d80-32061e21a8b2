<?php
require_once (__DIR__.'/../../constants.php');
require_once (__DIR__.'/../../connect_db.php');
require_once (__DIR__.'/../../app/controllers/__payment_validations.php');
require_once (__DIR__.'/../../app/models/payment_references.php');
require_once (__DIR__.'/../auth.php');
require_once (__DIR__.'/../../app/models/exception.php');
require_once (__DIR__.'/../../app/models/app_logs.php');
require_once (__DIR__.'/../../app/controllers/cors.php');

cors();

(function(){
try{
    
    $user = validateUser();

    $json = file_get_contents('php://input');
    $data = json_decode($json, true);

    $driver = new DBDriver;
    $con = $driver->connectPgSql(PG_FINANCE_DB_NAME);

    if($_SERVER['REQUEST_METHOD'] == 'GET'){
        http_response_code(200);
        $references = PaymentReferences::selectMany($con);
        $response = setResponse(true,"Payments references successfully fetched",$references,null);
        echo json_encode($response);
    }


    else if($_SERVER['REQUEST_METHOD'] == 'POST')
    {
    
        if(!isValidReferencepayload($data)) throw new CustomException(400,"One or more parameters are missing or invalid");
        $data['reference'] = generateReference($data['id']);
        PaymentReferences::insertOne($con, $data);
        http_response_code(200);
        $response = setResponse(true,"Payment reference successfully created",[$data['id']=>$data['reference']],null);
        AppLogs::insertOne($con,generateLogs($user->data->staff_id,"CREATED REFERENCE", $data));
        echo json_encode($response);
    }
    else{
        throw new CustomException(403,"Request Method Not Allowed");
}
}
catch(CustomException $e){
    http_response_code($e->status ?? 500);
    echo json_encode(setResponse(false,"Something went wrong.. ",[], $e->getMessage()));
}
$con= null; 
})()
?>