<?php
require_once(__DIR__ . "/../controllers/__data_helpers.php");


class WediLMRCards{
    /* selectPending : Runs a select query on wedi_lmr_cards only selecting  not yet transferred records
         * @param $con : database connection object
         * returns - null
         */
    public static function selectPending($con){
        try{
            $query = "SELECT * FROM wedi_lmr_cards WHERE (recipient_id IS NULL OR NOT recipient_id ILIKE '%RCP%')
                      AND LENGTH(account_number) = 10  AND profiling_attempts < 2 LIMIT 100";
            $stmt = $con->prepare($query);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $err) {
            throw new Exception("Ooops!!! Looks like we couldn't fetch all WEDI LMR card records - " . $err->getMessage());
        }
    }

    public static function updateOne($con, $id, $update_pairs)
    {
        $update_string = '';
        try {
            // build up the update string
            $index = 0;
            foreach ($update_pairs as $key => $value) {
                $update_string .= "{$key} ='{$value}'";
                if($index < count($update_pairs) - 1 )
                $update_string .= ",";
                ++$index;
            }
            $stmt = $con->prepare("UPDATE wedi_lmr_cards SET {$update_string} WHERE id = ?");
            $stmt->execute([$id]);
        } catch (Exception $err) {
            throw new Exception("Ooops!!! Looks like we couldn't update all WEDI LMR card records - " . $err->getMessage());
        }
    }
}

?>