<?php
require_once '../connect_db.php';
require_once '../constants.php';
require_once '../app/controllers/cors.php';
require_once ('auth.php');

cors();

$user = validateUser();


$json = file_get_contents('php://input');
$data = json_decode($json, true);

$type = $_GET['type'];

$code = 0;

if (!in_array($type, ["float_payment", "tg_harvest_payment", "aggregate_payment"])) {
    $response = get_response(false, "Please provide a valid config type", null);
    $code = 400;
} elseif ($type == "float_payment") {
    $sql = "SELECT type,hub_classification,transport_cost_per_bag,laborer_cost_per_bag,pay_until,repeats_every,time_of_payment FROM payment_portal_config WHERE type = ?";
} elseif ($type == "tg_harvest_payment") {
    $sql = "SELECT type, product_type, variety, transport_cost_per_bag, transport_cost_per_bag, processing_cost_per_bag, threshing_cost_per_ha, pay_until, repeats_every, time_of_payment, price_per_kg, similar_varieties FROM payment_portal_config WHERE type = ?";
} elseif ($type == "aggregate_payment") {
    $sql = "SELECT type, product_type, variety, pay_until, repeats_every, time_of_payment, price_per_kg, similar_varieties FROM payment_portal_config WHERE type = ?";
}

if ($code != 400) {
    try {
        $driver = new DBDriver;
        $conn_inventory = $driver->connectPgSql(PG_FINANCE_DB_NAME);
        $stmt = $conn_inventory->prepare($sql);

        $result = $stmt->execute([$type]);

        if ($result) {
            $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $res = array();
            foreach ($data as $datum) {
                $datum["repeats_every"] = explode(",", $datum["repeats_every"]);
                $res[] = $datum;
            }
            $response = get_response(true, $res, null);
            $code = 200;
        } else {
            $response = get_response(false, "Unable to add configuration", $stmt->errorInfo());
            $code = 400;
        }
    } catch (PDOException $exception) {
        $response = get_response(false, "Unable to add configuration", $exception->getMessage());
        $code = 400;
    }
}


http_response_code($code);
echo json_encode($response);

function get_response($status, $message, $error)
{
    return [
        'success' => $status,
        'message' => $message,
        'error' => $error
    ];
}

?>