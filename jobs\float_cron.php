<?php

require_once(__DIR__ . '/../connect_db.php');
require_once(__DIR__ . '/../constants.php');
require_once(__DIR__ . '/../app/controllers/payment_status.php');
require_once(__DIR__ . "/../app/controllers/Logger.php");
require_once(__DIR__ . '/../app/models/exception.php');


function fetch_paid_result($conn_finance)
{
    try {
        $sql = "SELECT payee_id, SUM(amount) as amount_paid FROM harvest_transactions WHERE payment_type = 'float_payment' GROUP BY payee_id";

        $stmt = $conn_finance->prepare($sql);
        $stmt->execute();
        $result = [];
        while ($data = $stmt->fetch()) {
            $key = $data["payee_id"];
            $result[$key] = $data["amount_paid"];
        }

        return $result;
    } catch (CustomException $e) {
        error_log('Fetch Paid Result Error: ' . $e->getMessage());
        return [];
    }
}

function fetch_cc_result_payment($conn_finance, $conn_inventory)
{
    try {
        // Fetching data from the financial_transactions database
        $sql_finance = "
            SELECT 
                id, 
                hub_name AS hub, 
                name, 
                role, 
                opening_balance, 
                rate, 
                recipient_id,
                collection_center_id 
            FROM 
                float_payment_config
            WHERE 
                id NOT IN (SELECT DISTINCT payee_id FROM payment_queue)
        ";

        $stmt_finance = $conn_finance->prepare($sql_finance);
        $stmt_finance->execute();

        $finance_results = $stmt_finance->fetchAll(PDO::FETCH_ASSOC);

        $sql_inventory_transporter = "
            SELECT 
                transporter_id, 
                SUM(bags_received) AS bags_received,
                SUM(total_cost) AS total_cost
            FROM 
                transport_controller
            WHERE
                deactivate_flag = 0
            GROUP BY 
                transporter_id
        ";

        $sql_inventory_labourer = "
            SELECT 
                collection_center_id, 
                SUM(bags_marketed) AS bags_marketed
            FROM 
                harvest_scaling_record
            GROUP BY 
                collection_center_id
        ";

        $stmt_inventory_transporter = $conn_inventory->prepare($sql_inventory_transporter);
        $stmt_inventory_transporter->execute();

        $stmt_inventory_labourer = $conn_inventory->prepare($sql_inventory_labourer);
        $stmt_inventory_labourer->execute();

        $transporter_results = $stmt_inventory_transporter->fetchAll(PDO::FETCH_ASSOC);

        $labourer_results = $stmt_inventory_labourer->fetchAll(PDO::FETCH_ASSOC);

        foreach ($finance_results as &$finance_result) {
            $finance_result['total_value'] = 0;  //default value
            if ($finance_result['role'] === 'Company Transporter' || $finance_result['role'] === 'Independent Transporter') {
                foreach ($transporter_results as $transporter_result) {
                    if ($finance_result['id'] === $transporter_result['transporter_id']) {
                        $finance_result['bag_quantity'] = $transporter_result['bags_received'];
                        $finance_result['total_value'] = $transporter_result['total_cost'];
                        break;
                    }
                }
            } elseif ($finance_result['role'] === 'Labourer') {
                foreach ($labourer_results as $labourer_result) {
                    if ($finance_result['collection_center_id'] === $labourer_result['collection_center_id']) {
                        $finance_result['total_value'] = $labourer_result['bags_marketed'] * $finance_result['rate'];
                        $finance_result['bag_quantity'] = $labourer_result['bags_marketed'];
                        break;
                    }
                }
            }
        }

        return $finance_results;
    } catch (Exception $e) {
        error_log('Fetch CC Result Payment Error: ' . $e->getMessage());
        return [];
    }
}




function process_results($cc_result_payment, $paid_result)
{
    $result = [];
    foreach ($cc_result_payment as $member) {
        $data = [];
        $cost_per_bag = $member["rate"];

        $num_bags = $member["bag_quantity"];

        $data["id"] = $member["id"];
        $data["recipient_id"] = $member["recipient_id"];
        $data["bags_handled"] = $num_bags;
        $data["cost_per_bag"] = $cost_per_bag;
        $data["hub"] = $member["hub"];
        $data["name"] = $member["name"];
        $data["role"] = $member["role"];
        $data["opening_balance"] = $member["opening_balance"];
        $data["total_value"] = $member["total_value"];
        $data["amount_paid"] = doubleval($paid_result[$member["id"]] ?? 0);
        $data["amount_to_be_paid"] = round($data["total_value"] - $data["amount_paid"], 2);
        $data["payment_status"] = get_payment_status($data["total_value"], $data["amount_paid"]);
        $data["payment_status_name"] = get_payment_status_name($data["payment_status"]);


        $result[] = $data;
    }

    return $result;
}

function insert_into_float_record($conn_finance, $processed_results)
{
    $sql = "
    INSERT INTO float_records 
    (id, recipient_id, no_of_bags, cost_per_bag, hub, name, role, opening_balance, total_value, amount_paid, amount_to_be_paid, payment_status, payment_status_name, created_at, updated_at) 
    VALUES 
    (:id, :recipient_id, :no_of_bags, :cost_per_bag, :hub, :name, :role, :opening_balance, :total_value, :amount_paid, :amount_to_be_paid, :payment_status, :payment_status_name, :created_at, :updated_at)
    ON CONFLICT (id) DO UPDATE SET
    recipient_id = EXCLUDED.recipient_id,
    no_of_bags = EXCLUDED.no_of_bags,
    cost_per_bag = EXCLUDED.cost_per_bag,
    hub = EXCLUDED.hub,
    name = EXCLUDED.name,
    role = EXCLUDED.role,
    opening_balance = EXCLUDED.opening_balance,
    total_value = EXCLUDED.total_value,
    amount_paid = EXCLUDED.amount_paid,
    amount_to_be_paid = EXCLUDED.amount_to_be_paid,
    payment_status = EXCLUDED.payment_status,
    payment_status_name = EXCLUDED.payment_status_name,
    updated_at = EXCLUDED.updated_at;
    ";

    $stmt = $conn_finance->prepare($sql);

    $current_date_time = date('Y-m-d H:i:s');

    foreach ($processed_results as $data) {
        $stmt->bindParam(':id', $data['id']);
        $stmt->bindParam(':recipient_id', $data['recipient_id']);
        $stmt->bindParam(':no_of_bags', $data['bags_handled']);
        $stmt->bindParam(':cost_per_bag', $data['cost_per_bag']);
        $stmt->bindParam(':hub', $data['hub']);
        $stmt->bindParam(':name', $data['name']);
        $stmt->bindParam(':role', $data['role']);
        $stmt->bindParam(':opening_balance', $data['opening_balance']);
        $stmt->bindParam(':total_value', $data['total_value']);
        $stmt->bindParam(':amount_paid', $data['amount_paid']);
        $stmt->bindParam(':amount_to_be_paid', $data['amount_to_be_paid']);
        $stmt->bindParam(':payment_status', $data['payment_status']);
        $stmt->bindParam(':payment_status_name', $data['payment_status_name']);
        $stmt->bindParam(':created_at', $current_date_time);
        $stmt->bindParam(':updated_at', $current_date_time);

        $stmt->execute();
    }
}

function fetchDeliveryCodeToPriceMapping($conn)
{
    $query = "SELECT code, price FROM price_code_mapping WHERE deactivate_flag = 0";

    $stmt = $conn->prepare($query);
    $stmt->execute();

    $results = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

    return $results;
}


function fetchDeliveryData($conn_inventory)
{
    $query = "
            SELECT 
                b.transport_info_id, 
                ah.unique_member_id, 
                ah.bags_received, 
                RIGHT(TRIM(b.delivery_code), 3) AS delivery_code, 
                b.transporter_id 
            FROM harvest_receiving_record ah 
            JOIN transport_info b ON ah.transport_info_id = b.transport_info_id 
            WHERE b.transport_mode = 'COMPANY'
            AND b.transport_info_id NOT IN (SELECT DISTINCT transport_info_id FROM transport_controller)
        ";

    $stmt = $conn_inventory->prepare($query);
    $stmt->execute();

    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    return $results;
}

function fetchDeliveryCodeAndCalculateTotalCost($deliveryData, $priceMapping)
{
    foreach ($deliveryData as &$data) {
        $deliveryCode = $data['delivery_code'];
        $pricePerBag = isset($priceMapping[$deliveryCode]) ? $priceMapping[$deliveryCode] : 0;

        $data['price_per_bag'] = $pricePerBag;
        $data['total_cost'] = $pricePerBag * $data['bags_received'];
    }
    return $deliveryData;
}

function insertIntoTransporterRecord($conn, $deliveryData, $length)
{
    $columnsArray = [
        "transport_info_id",
        "unique_member_id",
        "bags_received",
        "short_delivery_code",
        "transporter_id",
        "price_per_bag",
        "total_cost"
    ];

    $columnsString = join(",", $columnsArray);

    try {
        $chunks = array_chunk($deliveryData, $length);
        foreach ($chunks as $eachChunk) {
            $placeholderArray = [];
            for ($i = 0; $i < count($eachChunk); $i++) {
                $placeholderArray[] = "(?, ?, ?, ?, ?, ?, ?)";
            }
            $placeholderString = join(",", $placeholderArray);

            $query = "INSERT INTO transport_controller ({$columnsString}) VALUES {$placeholderString}";

            $stmt = $conn->prepare($query);

            $oneMultiInsertArray = [];
            foreach ($eachChunk as $eachRecord) {
                $oneMultiInsertArray[] = $eachRecord['transport_info_id'];
                $oneMultiInsertArray[] = $eachRecord['unique_member_id'];
                $oneMultiInsertArray[] = $eachRecord['bags_received'];
                $oneMultiInsertArray[] = $eachRecord['delivery_code'];
                $oneMultiInsertArray[] = $eachRecord['transporter_id'];
                $oneMultiInsertArray[] = $eachRecord['price_per_bag'];
                $oneMultiInsertArray[] = $eachRecord['total_cost'];
            }
            $stmt->execute($oneMultiInsertArray);
        }
    } catch (Exception $err) {
        throw new Exception("Unable to insert records - " . $err->getMessage());
    }
}



function initiate_float_processing($conn_finance, $conn_inventory, $conn_transport)
{
    try {
        $start = microtime(true);
        echo  date('Y-m-d H:i:s.u') . "  🎶🎶🎶  =====FLOAT CRON ACTIVATED, READY FOR TAKEOFF==== 🎶🎶🎶 <br /> \n";
        $logArray = [];

        echo  date('Y-m-d H:i:s.u') . " ✋✋......Now fetching Price and delivery code from table... ✋✋ <br /> \n";
        $fetchDeliveryCodeToPriceMapping = fetchDeliveryCodeToPriceMapping($conn_transport);

        echo  date('Y-m-d H:i:s.u') . " ✋✋......Now Bags received from harvest receiving table... ✋✋ <br /> \n";
        $deliveryData = fetchDeliveryData($conn_inventory);

        echo  date('Y-m-d H:i:s.u') . " ✋✋......Now calculating total cost... ✋✋ <br /> \n";
        $finalDeliveryData = fetchDeliveryCodeAndCalculateTotalCost($deliveryData, $fetchDeliveryCodeToPriceMapping);

        echo  date('Y-m-d H:i:s.u') . " ✋✋......Now inserting into transport controller table... ✋✋ <br /> \n";
        insertIntoTransporterRecord($conn_inventory, $finalDeliveryData, 5000);


        echo  date('Y-m-d H:i:s.u') . " ✋✋......Now fetching paid transactions from harvest transactions table... ✋✋ <br /> \n";
        $paid_result = fetch_paid_result($conn_finance);
        echo  date('Y-m-d H:i:s.u') . " ✔✔✔✔ ......Done fetching paid transactions...✔✔✔✔ <br /> \n";

        echo  date('Y-m-d H:i:s.u') . " ✋✋......Now fetching float transactions from respective tables... ✋✋ <br /> \n";
        $cc_result_payment = fetch_cc_result_payment($conn_finance, $conn_inventory);
        echo  date('Y-m-d H:i:s.u') . " ✔✔✔✔ ......Done fetching from inventory db...✔✔✔✔ <br /> \n";

        echo  date('Y-m-d H:i:s.u') . " ✋✋......Now processing results and making calculations... ✋✋ <br /> \n";
        $processed_results = process_results($cc_result_payment, $paid_result);
        echo  date('Y-m-d H:i:s.u') . " ✔✔✔✔ ......Done processing results...✔✔✔✔ <br /> \n";

        if (empty($processed_results)) {
            throw new Exception('Processed results are empty');
        }

        echo  date('Y-m-d H:i:s.u') . " ✋✋......Now inserting into float records table... ✋✋ <br /> \n";
        insert_into_float_record($conn_finance, $processed_results);

        $end = microtime(true);
        $elapsed = $end - $start;
        $logArray[] = getLogString("FLOAT CRON",  "🎵🎵🎵 Bravo!!!! FLOAT CRON ran successfully in " . $elapsed . " seconds 🎵🎵🎵");
        $logArray[] = getLogString("FLOAT CRON", '🔊🔊🔊🔊🔊 - JOB SUMMARY - 🔊🔊🔊🔊🔊');
        $logArray[] = getLogString("FLOAT CRON", '✍✍ - ' .  count($fetchDeliveryCodeToPriceMapping) . " - Price code mapping===");
        $logArray[] = getLogString("FLOAT CRON", '✍✍ - ' .  count($deliveryData) . " - Delivery data===");
        $logArray[] = getLogString("FLOAT CRON", '✍✍ - ' .  count($paid_result) . " - Paid results===");
        $logArray[] = getLogString("FLOAT CRON", '✍✍ - ' .  count($cc_result_payment) . " - records from inventory db===");
        $logArray[] = getLogString("FLOAT CRON", '✍✍ - ' .  count($processed_results) . " - records inserted into float table===");
        $logArray[] = getLogString("FLOAT CRON",  "👏👏👏 FLOAT CRON TOUCH DOWN SUCCESSFULLY...👏👏👏👏");

        foreach ($logArray as $log) {
            echo $log;
        }
    } catch (Exception $e) {
        error_log('Initiate Processing Error: ' . $e->getMessage());
        echo " 🙈🙈🙈...Something went wrong. Float cron failed: 🙈🙈🙈 " . $e->getMessage();
    }
}
