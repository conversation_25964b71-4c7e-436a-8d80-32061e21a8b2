<?php

require_once(__DIR__ . '/../connect_db.php');
require_once(__DIR__ . '/../scripts/pay_recipient.php');
require_once(__DIR__ . '/../app/controllers/__response_helpers.php');
require_once(__DIR__ . '/../app/models/harvest_tg_payments.php');
require_once(__DIR__ . '/../app/models/payment_queue.php');
require_once(__DIR__ . '/../app/models/app_logs.php');
require_once(__DIR__ . '/../app/models/payment_config.php');
require_once(__DIR__ . '/../app/models/payment_tracker.php');
require_once(__DIR__ . '/../jobs/float_cron.php');

date_default_timezone_set('Africa/Lagos');
// ini_set('max_execution_time', '1000');


if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] != 'GET') {
    http_response_code(403);
    echo (json_encode(setResponse(false, "Something went wrong", [], "Request Method Not Allowed")));
    exit;
}

function verifyOne($reference)
{
        $driver = new DBDriver;
        $con = $driver->connectPgSql(PG_FINANCE_DB_NAME);

        try {
                $configs = PaymentSecrets::selectMany($con);

                // Now make a request to Paystack Api                                                                                                 
                $ch = curl_init(PAYSTACK_BASE_URL . '/transfer/verify/' . $reference);
                curl_setopt($ch,  CURLOPT_ENCODING, "");
                curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
                curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                curl_setopt($ch,  CURLOPT_CUSTOMREQUEST, "GET");
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                        "Authorization: Bearer " . trim($configs->PAYSTACK_API_KEY),
                        "Cache-Control: no-cache",
                ));
                $response = curl_exec($ch);

                if (curl_errno($ch)) {
                        // Log cURL error
                        error_log("cURL Error (" . curl_errno($ch) . "): " . curl_error($ch));
                        return setResponse(false, "Something went wrong", [], curl_errno($ch));
                }

                $result = json_decode($response);
                return $result;
        } catch (Exception $e) {
                return ["status" => false, "error" => $e->getMessage()];
        }
}

(function () {
    try {
        $driver = new DBDriver;
        $conn = $driver->connectPgSql(PG_FINANCE_DB_NAME);

        //Batch id required per payment completed
        $batchId = uniqid('BTH-', true);


        $payments = PaymentQueue::selectAttempted($conn);
        $responseArray = [];
        $deleteArray = [];

        foreach ($payments as $one_payment) {
            try {
                $payload = json_decode($one_payment['payload']);
                $needed_payload = extractPayload($payload);
                $start = microtime(true);
                $result = verifyOne($one_payment['reference']);
                $end = microtime(true);

                $elapsed = $end - $start;
                
                $data = [
                    'payload' => $needed_payload,
                    'batch_id' => $batchId,
                    'result' => $result,
                    'elapsed_time' => $elapsed
                ];
                //print_r($data);

                if ($result->data->status == 'success') {
                    HarvestTransactions::insertOne($conn, [
                        "reference" => $needed_payload['reference'],
                        "payee_id" => $payload->payee_id,
                        "recipient_id" => $needed_payload['recipient'],
                        "payment_type" => $payload->payment_type,
                        "amount" => $needed_payload['amount'] / 100,
                        "code" => $result->data->transfer_code ?? 'transfer_code',
                        "transaction_date" => date('Y-m-d H:i:s'),
                        "description" => $result->data->reason ?? 'reason',
                        "batch_id" => $batchId,
                    ]);

                    PaymentTrackerHelper::updatePaymentStatus($conn, $needed_payload['reference'], 'COMPLETED');
                    $deleteArray[] = $one_payment['id'];

                    $responseArray[] = [
                        "success" => true,
                        "account_number" => $payload->account_number,
                        "amount" => $needed_payload['amount'] / 100,
                        "recipient" => $needed_payload['recipient'],
                        "message" => $result->message,
                        "reference" => $needed_payload['reference'],
                        // "attempts" => $one_payment['attempts'] + 1,
                    ];
                } else {
                    PaymentQueue::updateOne($conn, $one_payment['id'], [
                        "extra_info" => $result->data->failures ?? 'failed',
                    ]);
                    PaymentTrackerHelper::updatePaymentStatus($conn, $needed_payload['reference'], 'FAILED');

                    $responseArray[] = [
                        "success" => false,
                        "account_number" => $payload->account_number,
                        "amount" => $needed_payload['amount'] / 100,
                        "recipient_id" => $needed_payload['recipient'],
                        "payee_id" => $payload->payee_id,
                        "message" => $result->message,
                        "reference" => $needed_payload['reference'],
                        // "attempts" => $one_payment['attempts'] + 1,
                    ];
                }
                AppLogs::insertOne($conn, generateLogs("CRON", "PAYMENT VERIFICATION", $data));
            } catch (Exception $e) {
                $responseArray[] = [
                    "success" => false,
                    "reference" => $needed_payload['reference'],
                    "account_number" => $payload->account_number,
                    "message" => $e->getMessage(),
                ];
            }
        }

        // Delete completed payment records from the payment queue
        if (!empty($deleteArray)) {
            PaymentQueue::deleteMany($conn, $deleteArray);
        }

        echo json_encode($responseArray);
    } catch (Exception $e) {
        $responseArray[] = json_encode(setResponse(false, "Something went wrong...", [], $e->getMessage()));
        echo json_encode($responseArray);
    }
})();

?>