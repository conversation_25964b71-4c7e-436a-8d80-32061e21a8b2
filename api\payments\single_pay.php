<?php
require_once (__DIR__.'/../../constants.php');
require_once (__DIR__.'/../../connect_db.php');
require_once (__DIR__.'/../../app/controllers/__payment_validations.php');
require_once (__DIR__.'/../auth.php');
require_once (__DIR__.'/../../app/models/exception.php');
require_once (__DIR__.'/../../app/models/payment_queue.php');
require_once (__DIR__.'/../../app/models/harvest_tg_payments.php');
require_once (__DIR__.'/../../app/controllers/cors.php');
require_once (__DIR__.'/../../app/models/app_logs.php');


cors();

// (function(){
//     try{
//         if($_SERVER['REQUEST_METHOD'] != 'POST') throw new CustomException(403,"Request Method Not Allowed");
//         $user = validateUser();
//         if(!$user) throw new CustomException(401, "Invalid Request. User could not be authenticated");
//         $driver = new DBDriver;
//         $conn = $driver->connectPgSql(PG_FINANCE_DB_NAME);
//         $json = file_get_contents('php://input');
//         $data = json_decode($json, true);
        
//         if(!isValidPaymentPayload($data)) throw new CustomException(400,"Invalid payment payload, one or more parameters are missing or invalid");
//         $data["reference"] = generateReference($data["payee_id"]);

//         if($data['payment_type'] == 'harvest_advance'){
//             $tg_info = TGPayments::selectCustom($conn, ['payment_ready'], ["ik_number"=> $data['payee_id']]);
//             if(!$tg_info) throw new CustomException(400,"Invalid payee");
//             if($tg_info[0]['payment_ready'] == 0) throw new CustomException(400,"Invalid payment payload, this TG cannot be paid yet");
//         }
//         PaymentQueue::insertOne($conn, $data);
//         http_response_code(201);
//         $payment_response = setResponse(true,"Payment has been successfully queued", [] , null);
//         echo json_encode($payment_response);
//         AppLogs::insertOne($conn,generateLogs($user->data->staff_id,"SUBMITTED SINGLE PAYMENT", $data));
//     }
//     catch(CustomException $e){
//         http_response_code($e->status ?? 500);
//         if(str_contains($e->getMessage(), 'duplicate')) http_response_code(403);
//         echo json_encode(setResponse(false,"Something went wrong... Payment not queued ",[], $e->getMessage()));
//     }
// })()


?>