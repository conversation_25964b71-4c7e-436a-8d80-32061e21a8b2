<?php
ini_set('memory_limit', '2048M');
//ini_set('max_execution_time', '1000');
ini_set('max_execution_time', '-1');
error_reporting(E_ERROR);

require_once(__DIR__ . "/connect_db.php");
require_once(__DIR__ . "/constants.php");
require_once(__DIR__ . "/jobs/cron_0.php");
require_once(__DIR__ . "/jobs/cron_3.php");
require_once(__DIR__ . "/jobs/cron_1.php");
require_once(__DIR__ . "/jobs/cron_2.php");
require_once(__DIR__ . "/scripts/hg.php");


// Establish DB Connections
$driver = new DBDriver;
$conn_inventory = $driver->connectPgSql(PG_INVENTORY_DB_NAME);
$conn_finance = $driver->connectPgSql(PG_FINANCE_DB_NAME);
$conn_farming = $driver->connectPgSql(PG_FARMING_DB_NAME);
$conn_transport = $driver->connectPgSql(PG_TRANSPORTATION_DB_NAME);
$conn_mkt = $driver->connectMKT();
date_default_timezone_set('Africa/Lagos');
echo "Version: v0.0.10 \n";


// Fetch Config Values from the Database
$configDbOperations = new ConfigDbOperations($conn_inventory);
$configModel = $configDbOperations->get_data();


//Execute cron functions one after the other
executeCron0($conn_inventory, $conn_farming, $configModel, $conn_finance);
$cron1Job = new ValidateClearedTableJob($conn_inventory);
$logs = $cron1Job->validate_cleared_data($configModel, $conn_transport, $conn_inventory, $conn_farming);
foreach ($logs as $log) {
   echo $log;
}
$cron2Job = new Cron2($conn_finance, $conn_inventory, $conn_transport);
$cron2logs = $cron2Job->startCron($configModel, $conn_farming, $conn_inventory, $conn_transport);
foreach ($cron2logs as $log) {
    echo $log;
}
executeCron3($conn_finance, $conn_mkt, $configModel);

//_processHarvestHGs($conn_mkt, $conn_inventory, $conn_finance);



// Close db connections
$conn_inventory = null;
$conn_finance = null;
$conn_mkt = null;
?>
