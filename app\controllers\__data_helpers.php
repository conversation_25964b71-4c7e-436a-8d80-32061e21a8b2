<?php

/*getHSFId
    * extracts an HSF id from an assoc array of a single table record or row
    * args : $record - an associative array
    * returns null
*/
function getHSFId($record){
    return $record["hsf_id"];
} 

function getIkNumber($record){
    return $record['ik_number'];
}

function generateDuplicateStringIN($arr){
    try{
        $duplicate_string = '';
        foreach($arr as $key=>$value){
           $duplicate_string .= $value."=VALUES(".$value.")";
           if($key < count($arr) - 1) $duplicate_string .= ',';
        }
        return $duplicate_string;
    }
    catch(Exception $e){
        throw new Exception("Error, could not generate duplicate String - ". $e->getMessage());
    }

}


function generateDuplicateStringPG($arr, $excludedArr){
    try{
        $duplicate_string = '';
        $first_half ='(';
        $second_half = '(';

        foreach($arr as $key=>$value){
        if(in_array($value, $excludedArr)) continue;
           $first_half  .= $value;
           $second_half .= 'EXCLUDED.'.$value;

           if($key == count($arr) - 1){
                    $first_half  .= ')';
                    $second_half .= ')'; 
           }
           else{
                    $first_half  .= ',';
                    $second_half .= ',';
           }
        }
        $duplicate_string = $first_half. " = ". $second_half;
        return $duplicate_string;
    }
    catch(Exception $e){
        throw new Exception("Error, could not generate duplicate String - ". $e->getMessage());
    }
}

function onDNPList($dnp_arr, $record ){
    try{
       return in_array($record['ik_number'], $dnp_arr) ;
    }
    catch(Exception $e){
        throw new Exception($e->getMessage());
    }  
}

function getMinPayout($record, $config ){
    try{
               if(strtolower($record['product']) == 'maize') return  $config->min_payout_config_maize;
               if(strtolower($record['product']) == 'rice') return $config->min_payout_config_rice;
               if(str_contains(strtolower($record['product']), 'soy')) return $config->min_payout_config_soy;
               return $config->min_payout_config_maize;        
    }
    catch(Exception $e){
        throw new Exception($e->getMessage());
    }  
}

function getMaxPayout($record, $config){
    try{
        if(strtolower($record['product']) == 'maize') return  $config->max_payout_config_maize;
        if(strtolower($record['product']) == 'rice') return $config->max_payout_config_rice;
        if(str_contains(strtolower($record['product']), 'soy')) return $config->max_payout_config_soy;
        return $config->max_payout_config_maize;
    }
    catch(Exception $e){
        throw new Exception($e->getMessage());
    }  
}

function getActiveTG($arr){
    try {
        $driver = new DBDriver;
        $conn_inv = $driver->connectPgSql(PG_INVENTORY_DB_NAME);

        if (empty($arr)) return [];
        $placeholder_string = join(",", array_fill(0,count($arr), "?"));
        $query = "SELECT DISTINCT ik_number FROM tgetg_inventory_distributed_entity WHERE status = '1' AND ik_number IN ({$placeholder_string})";
        $stmt = $conn_inv->prepare($query);
        $stmt->execute(array_values($arr));
        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    } catch (PDOException $e) {
        throw new Exception("Ooops!!! Looks like we couldn't fetch all TG Leader records - ". $e->getMessage());
    }
}

function getTGLeaders($arr){
    try {
        $driver = new DBDriver;
        $conn_rec = $driver->connectPgSql(PG_RECRUITMENT_DB_NAME);

        $tg_arr = getActiveTG($arr);
        

        if (empty($tg_arr)) return [];

        // $tgl_arr = [];
        // foreach ($tg_arr as $key) {
        //     if (isset($key['ik_number'])) {
        //         $tgl_arr[] = $key['ik_number'];
        //     }
        // }
        $placeholder_string = join(",", array_fill(0,count($tg_arr), "?"));
        $query = "SELECT m.unique_member_id AS member_id, m.ik_number, concat(m.first_name, ' ', m.last_name) AS payee_name, m.phone_number, m.community_name, m.role FROM members_entity m JOIN clearance_members c ON m.unique_member_id = c.unique_member_id WHERE m.ik_number IN ({$placeholder_string}) AND m.role = 'Leader' AND c.clearance_field_size > '0'";
        $stmt = $conn_rec->prepare($query);
        $stmt->execute(array_values($tg_arr));
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        throw new Exception("Ooops!!! Looks like we couldn't fetch all TG Leader records - ". $e->getMessage());
    }
}

function getTGHubs($conn, $arr = null){
    try {
        if (empty($arr)) {
            $query = "SELECT id, name FROM hub_entity ORDER BY name ASC";
        } else {
            $placeholder_string = join(",", array_fill(0,count($arr), "?"));
            $query = "SELECT id as hub_id, name as hub_name FROM hub_entity WHERE id IN ({$placeholder_string})";
        }
        $stmt = $conn->prepare($query);
        if (!empty($arr)) {
            $stmt->execute(array_values($arr));
        } else {
            $stmt->execute();
        }
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        throw new Exception("Ooops!!! Looks like we couldn't fetch all TG Hubs records - ". $e->getMessage());
    }
}

function getStaffNames($conn, $arr = null){
    try {
        if (empty($arr)) {
            $query = "SELECT id, concat(first_name, ' ', last_name) as name FROM staff";
        } else {
            $placeholder_string = join(",", array_fill(0,count($arr), "?"));
            $query = "SELECT id, concat(first_name, ' ', last_name) as name FROM staff WHERE id IN ({$placeholder_string})";
        }
        $stmt = $conn->prepare($query);
        if (!empty($arr)) {
            $stmt->execute(array_values($arr));
        } else {
            $stmt->execute();
        }
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        throw new Exception("Ooops!!! Looks like we couldn't fetch all Staff records - ". $e->getMessage());
    }
}

function getDNPReason($conn, $type){
    try {
        $query = "SELECT id, dnp_reason FROM dnp_reasons WHERE dnp_category ILIKE ? ORDER BY dnp_reason ASC";
        $stmt = $conn->prepare($query);
        $stmt->execute([$type]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        throw new Exception("Ooops!!! Looks like we couldn't fetch all DNP reasons - ". $e->getMessage());
    }
}