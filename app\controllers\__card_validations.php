<?php

function validateMemberCardPayload($data)
{
    $errors = [];

    if (empty($data)) {
        $errors[] = "Card data is empty.";
    } else {
        if (empty($data['id'])) {
            $errors[] = "Member ID is missing.";
        }
        if (empty($data['category'])) {
            $errors[] = "Member category is missing.";
        }
        if (empty($data['account_number'])) {
            $errors[] = "Card account number is missing.";
        }
        if (empty($data['account_name'])) {
            $errors[] = "Account name is missing.";
        }
        if (empty($data['bank_name'])) {
            $errors[] = "Bank name is missing.";
        }
        if (strlen($data['account_number']) != 10) {
            $errors[] = "Invalid account number.";
        }
        if (strpos($data['account_name'], 'BABBAN GONA') !== 0) {
            $errors[] = "Invalid account name.";
        }        
    }
    return $errors;
}
?>