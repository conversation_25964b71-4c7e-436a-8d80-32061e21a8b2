<?php
require_once '../connect_db.php';
require_once '../constants.php';
require_once '../app/controllers/cors.php';
require_once('auth.php');
require_once(__DIR__ . '/../app/controllers/__response_helpers.php');
require_once(__DIR__ . '/../app/models/app_logs.php');


cors();

$user = validateUser();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $user = validateUser();

    $json = file_get_contents('php://input');
    $data = json_decode($json, true);

    if (!isset($data['personnel_id']) || !is_array($data['personnel_id'])) {
        throw new CustomException(400, "Payee ID(s) missing or not in the correct format");
    }

    try {
        $driver = new DBDriver;
        $conn_finance = $driver->connectPgSql(PG_FINANCE_DB_NAME);
    
        $payee_ids_array = '{' . implode(',', $data['personnel_id']) . '}';
    
        $sql = "UPDATE float_payment_config SET freeze_flag = 0 WHERE id = ANY(:payee_id)";
    
        $stmt = $conn_finance->prepare($sql);
    
        $result = $stmt->execute([':payee_id' => $payee_ids_array]);
    
        if ($result) {
            http_response_code(200);
            echo json_encode(setResponse(true, "Personnel payment has been unfrozen", [], ""));
            AppLogs::insertOne($conn_finance, generateLogs($user->data->staff_id, "USER UNFROZE PERSONNEL", $data));
        } else {
            throw new Exception("Failed to unfreeze payment");
        }
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(setResponse(false, "Something went wrong", [], $e->getMessage()));
    }
    
} else {
    http_response_code(405);
    echo json_encode(setResponse(false, "Something went wrong", [], "Method not allowed"));
}
