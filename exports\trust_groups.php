<?php

require_once(__DIR__.'/../connect_db.php');
require_once(__DIR__.'/../constants.php');
require_once '../app/controllers/cors.php';
require_once (__DIR__.'/../app/models/harvest_tg_payments.php');
require_once (__DIR__.'/../app/models/harvest_transactions.php');
require_once '../api/payment_helper.php';
require_once(__DIR__.'/../app/models/exception.php' );
require_once(__DIR__ . '/../api/auth.php');

cors();

validateUser();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    throw new CustomException(405, "Method Not Allowed. Only POST requests are allowed.");
}

$input = json_decode(file_get_contents('php://input'), true);   
//Check if the required parameters are set
$id = $input['id'] ?? null;

$driver = new DBDriver;
$conn = $driver->connectPgSql(PG_FINANCE_DB_NAME);

date_default_timezone_set('Africa/Lagos');
$dates = date("Y-m-d");
$file = "harvest_advance";//File Name
$filename = $file. "-".$dates;

$response = TGPayments::selectPaymentReady($conn, $id);

extract_trust_group_file($response, $filename);

?>