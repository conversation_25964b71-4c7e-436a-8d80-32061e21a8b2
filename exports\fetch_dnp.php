<?php
require_once(__DIR__ . '/../api/auth.php');
require_once(__DIR__ . '/../app/controllers/cors.php');
require_once(__DIR__ . '/../constants.php');
require_once(__DIR__ . '/../connect_db.php');
require_once(__DIR__ . '/../app/models/app_logs.php');
require_once(__DIR__ . '/../app/models/fetch_dnp.php');
require_once("../app/controllers/__data_helpers.php");
require_once(__DIR__.'/../app/models/exception.php' );

cors();

function exportDNP()
{
    try {
        $user = validateUser();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            throw new CustomException(405, "Method Not Allowed. Only POST requests are allowed.");
        }

        $input = json_decode(file_get_contents('php://input'), true);
        
        //Check if the required parameters are set
        $id = $input['id'] ?? null;

        $driver = new DBDriver;
        $conn_finance = $driver->connectPgSql(PG_FINANCE_DB_NAME);
        $conn_portfolio = $driver->connectPgSql(PG_PORTFOLIO_DB_NAME);
        $conn_planning = $driver->connectPgSql(PG_PLANNING_DB_NAME);
        $conn_bg_signon = $driver->connectPgSql(PG_BG_SIGNON_DB_NAME);

        $columns = ['member_id', 'ik_number', 'payee_name', 'phone_number', 'community_name', 'role', 'hub_id', 'hub_name', 'dnp_category', 'date_logged', 'date_solved', 'reason', 'status', 'comment_created', 'comment_solved', 'created_by', 'resolved_by'];

        date_default_timezone_set('Africa/Lagos');
        $dates = date("Y-m-d H:i:s"); // Year-Month-Day Hour:Minute:Second
        $tableName = 'dnp';
        $filename = $tableName . "-" . $dates;
        header('Content-Type: text/csv');
        header("Content-Disposition: attachment;filename=\"{$filename}.csv\"");

        $fp = fopen('php://output', 'w');

        fputcsv($fp, $columns);

        $response = get_DNP($conn_finance, $conn_portfolio, $conn_planning, $conn_bg_signon, $id);

        foreach ($response as $row) {
            // Open the rsponse in append mode ('a')
            $fp = fopen('php://output', 'a');
    
            // Write values into the columns
            $rowData = [
                $row['member_id'],
                $row['ik_number'],
                $row['payee_name'],
                $row['phone_number'],
                $row['community_name'],
                $row['role'],
                $row['hub_id'],
                $row['hub_name'],
                $row['dnp_category'],
                $row['date_logged'],
                $row['date_solved'],
                $row['reason'],
                $row['status'],
                $row['comment_created'],
                $row['comment_solved'],
                $row['creator_name'],
                $row['resolver_name']
            ];
    
            // Write the data to the CSV file
            fputcsv($fp, $rowData);
    
            // Close the output stream
            fclose($fp);
        }

        // Logging the user's action
        AppLogs::insertOne($conn_finance, generateLogs($user->data->staff_id, "USER EXPORTED ALL DNP", ""));
    } catch (CustomException $e) {
        http_response_code($e->status ?? 500);
        echo json_encode(['status' => false, 'message' => $e->getMessage()]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['status' => false, 'message' => $e->getMessage()]);
    }
}


exportDNP();

?>
